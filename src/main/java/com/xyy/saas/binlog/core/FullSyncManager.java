package com.xyy.saas.binlog.core;

import com.xyy.saas.binlog.config.detail.FullSyncConfig;
import com.xyy.saas.binlog.config.detail.SyncTaskConfig;
import com.xyy.saas.binlog.config.detail.TableMapping;
import com.xyy.saas.binlog.core.data.DataSource;
import com.xyy.saas.binlog.core.data.DataSourceFactory;
import com.xyy.saas.binlog.core.data.DynamicDataSourceManager;
import com.xyy.saas.binlog.core.data.GenericDao;
import com.xyy.saas.binlog.core.progress.*;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;

@Slf4j
@RequiredArgsConstructor
public class FullSyncManager {

    @Resource
    private final DataSyncProcessor dataSyncProcessor;

    @Resource
    private final SyncProgressRepository progressRepository;

    @Resource
    private final ConfigManager configManager;

    @Resource
    private final ThreadPoolManager threadPoolManager;

    @Resource
    private final DynamicDataSourceManager dynamicDataSourceManager;

    private final Map<String, Future<?>> runningTasks = new ConcurrentHashMap<>();

    /**
     * 启动全量同步
     */
    public void startFullSync(String taskId) {
        SyncTaskConfig config = configManager.getTaskConfig(taskId);
        if (config == null) {
            throw new IllegalArgumentException("Task config not found: " + taskId);
        }
        if (!config.isEnabled() || config.getTableMapping() == null) {
            throw new IllegalArgumentException("Task is not enabled or table mapping is missing: " + taskId);
        }
        if (config.getFullSyncConfig() == null || !config.getFullSyncConfig().isEnabled()) {
            throw new IllegalArgumentException("Full sync is not enabled for task: " + taskId);
        }

        // 检查是否已在运行
        if (runningTasks.containsKey(taskId)) {
            log.warn("[binlog-sync] Full sync task already running: {}", taskId);
            return;
        }

        Future<?> future = threadPoolManager.getFullSyncExecutor().submit(() -> executeFullSync(config));
        runningTasks.put(taskId, future);
        log.info("[binlog-sync] Full sync task started: {}", taskId);
    }

    /**
     * 停止全量同步
     */
    public void stopFullSync(String taskId) {
        Future<?> future = runningTasks.remove(taskId);
        if (future != null) {
            future.cancel(true);
            // 更新进度状态为已取消
            updateSyncStatus(taskId, SyncStatus.CANCELLED);
        }
    }

    /**
     * 恢复全量同步（断点恢复）
     */
    public void resumeFullSync(String taskId) {
        List<SyncProgress> progresses = progressRepository.findAllByTaskId(taskId);
        boolean hasPaused = progresses.stream().anyMatch(p -> p.getStatus() == SyncStatus.PAUSED);
        if (!hasPaused) {
            log.warn("[binlog-sync] Cannot resume task {}, no paused thread progress found", taskId);
        } else {
            log.info("[binlog-sync] Resuming full sync for task {} using thread-level checkpoints", taskId);
        }
        startFullSync(taskId);
    }

    /**
     * 执行全量同步
     */
    private void executeFullSync(SyncTaskConfig config) {
        String taskId = config.getTaskId();

        try {
            executeTableFullSync(config, config.getTableMapping());

            updateSyncStatus(taskId, SyncStatus.COMPLETED);
            log.info("[binlog-sync] Full sync completed for task: {}", taskId);

        } catch (InterruptedException e) {
            log.info("[binlog-sync] Full sync interrupted for task: {}", taskId);
            updateSyncStatus(taskId, SyncStatus.PAUSED);
            Thread.currentThread().interrupt();

        } catch (Exception e) {
            log.error("[binlog-sync] Full sync failed for task: {}", taskId, e);
            updateSyncStatus(taskId, SyncStatus.FAILED, e.getMessage());
        } finally {
            runningTasks.remove(taskId);
        }
    }

    /**
     * 执行单表全量同步
     */
    private void executeTableFullSync(SyncTaskConfig config, TableMapping tableMapping)
            throws InterruptedException {

        String taskId = config.getTaskId();
        String tableName = tableMapping.getTableName();
        FullSyncConfig fullSyncConfig = config.getFullSyncConfig();

        // 不再持久化表级聚合进度，改为仅记录线程级进度
        // 计算总记录数（如有需要用于监控/日志）
        long totalRecords = countTotalRecords(config, tableName, fullSyncConfig.getWhereCondition());
        log.info("[binlog-sync] Starting full sync for table {} (task {}) total={}", tableName, taskId, totalRecords);

        // 多线程并行同步（线程级断点由各线程自行恢复）
        executeParallelSync(config, tableMapping);
    }

    /**
     * 并行同步执行
     */
    private void executeParallelSync(SyncTaskConfig config, TableMapping tableMapping) {

        FullSyncConfig fullSyncConfig = config.getFullSyncConfig();
        int threadCount = fullSyncConfig.getThreadCount();
        String tableName = tableMapping.getTableName();
        String primaryKey = tableMapping.getPrimaryKey();

        // 使用共享的全量同步线程池（命名为 full-sync-N），不在此处关闭
        ExecutorService syncExecutor = threadPoolManager.getFullSyncExecutor();
        List<CompletableFuture<?>> futures = new ArrayList<>(threadCount);

        // 计算每个线程的数据范围
        List<SyncRange> ranges = calculateSyncRanges(config, tableName, primaryKey, fullSyncConfig.getWhereCondition(),
                threadCount, null);

        // 预创建线程级进度记录，确保可见
        for (int i = 0; i < ranges.size(); i++) {
            ensureThreadProgressExists(config.getTaskId(), tableName, i);
        }

        // 为每个线程创建同步任务
        for (int i = 0; i < ranges.size(); i++) {
            final int threadIndex = i;
            final SyncRange range = ranges.get(i);

            futures.add(CompletableFuture.runAsync(() -> {
                try {
                    executeSyncRange(config, tableMapping, range, threadIndex);
                } catch (Exception e) {
                    log.error("[binlog-sync] Thread {} sync failed for range {}", threadIndex, range, e);
                    throw new RuntimeException(e);
                }
            }, syncExecutor));
        }

        // 等待所有线程完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    /**
     * 执行指定范围的同步
     */
    private void executeSyncRange(SyncTaskConfig config, TableMapping tableMapping,
                                  SyncRange range, int threadIndex) {

        String tableName = tableMapping.getTableName();
        FullSyncConfig fullSyncConfig = config.getFullSyncConfig();
        String primaryKey = tableMapping.getPrimaryKey();
        int batchSize = fullSyncConfig.getBatchSize();
        int threadSleepDelay = fullSyncConfig.getThreadSleepDelay();
        // 线程级进度
        SyncProgress threadProgress = getOrCreateThreadProgress(config.getTaskId(), tableName, threadIndex);
        Object currentValue = threadProgress.getLastSyncValue() != null ? threadProgress.getLastSyncValue() : range.getStartValue();
        long processedInThread = 0;
        long lastCheckpointTime = System.currentTimeMillis();

        while (currentValue != null && !Thread.currentThread().isInterrupted()) {
            try {
                // 构建查询SQL
                String sql = GenericDao.buildRangeSql(tableName, primaryKey, currentValue,
                        range.getEndValue(), batchSize,
                        fullSyncConfig.getWhereCondition(),
                        fullSyncConfig.getOrderBy());

                // 查询数据
                List<Map<String, Object>> rows = dynamicDataSourceManager.getGenericDao(config.getDataSourceId()).queryForMapList(sql);

                if (rows.isEmpty()) {
                    break;
                }

                // 使用DataSyncProcessor处理数据
                DataSource dataSource = DataSourceFactory.fromQueryResult(config.getDataSourceId(), tableName, rows);
                DataSyncProcessor.SyncResult result = dataSyncProcessor.processData(dataSource, config.getTaskId());

                // 更新线程级统计
                synchronized (threadProgress) {
                    threadProgress.setProcessedRecords(threadProgress.getProcessedRecords() + rows.size());
                    if (result.isSuccess()) {
                        threadProgress.setSuccessRecords(threadProgress.getSuccessRecords() + result.getSuccessCount());
                        threadProgress.setFailedRecords(threadProgress.getFailedRecords() + result.getFailedCount());
                    } else {
                        threadProgress.setFailedRecords(threadProgress.getFailedRecords() + rows.size());
                        threadProgress.setErrorMessage(result.getErrorMessage());
                    }
                    threadProgress.setLastUpdateTime(LocalDateTime.now());
                }

                // 如果处理成功，更新当前值
                if (result.isSuccess()) {
                    // 更新当前值为本批次最后一个记录的主键值
                    currentValue = rows.getLast().get(primaryKey);
                    processedInThread += rows.size();
                }

                // 检查点保存
                long currentTime = System.currentTimeMillis();
                if (fullSyncConfig.isEnableCheckpoint() && currentTime - lastCheckpointTime > fullSyncConfig.getCheckpointInterval()) {

                    synchronized (threadProgress) {
                        saveCheckpoint(threadProgress, currentValue);
                    }
                    lastCheckpointTime = currentTime;

                    log.debug("[binlog-sync] Thread {} checkpoint saved, processed: {}, current value: {}", threadIndex, processedInThread, currentValue);
                }

                // 如果处理失败，直接跳出循环，防止漏掉数据
                // 如果批次数量小于batchSize，说明已到达范围末尾
                if (!result.isSuccess() || rows.size() < batchSize) {
                    break;
                }

                if (threadSleepDelay > 0) {
                    TimeUnit.MILLISECONDS.sleep(threadSleepDelay); // 线程休眠
                }

            } catch (Exception e) {
                log.error("[binlog-sync] Error processing range in thread {}: {}", threadIndex, range, e);

                synchronized (threadProgress) {
                    threadProgress.setFailedRecords(threadProgress.getFailedRecords() + batchSize);
                    threadProgress.setErrorMessage(e.getMessage());
                    threadProgress.setLastUpdateTime(LocalDateTime.now());
                }

                // 可以选择继续或中断
                if (shouldStopOnError(config)) {
                    throw new RuntimeException(e);
                }
            }
        }

        // 标记线程完成
        synchronized (threadProgress) {
            threadProgress.setStatus(SyncStatus.COMPLETED);
            threadProgress.setLastUpdateTime(LocalDateTime.now());
            progressRepository.save(threadProgress);
        }
        log.info("[binlog-sync] Thread {} completed, processed {} records", threadIndex, processedInThread);
    }

    /**
     * 计算同步范围
     */
    private List<SyncRange> calculateSyncRanges(SyncTaskConfig config, String tableName, String primaryKey, String whereCondition, int threadCount, Object startValue) {

        // 获取主键的最小值和最大值
        String minMaxSql = GenericDao.buildMinMaxSql(tableName, primaryKey, whereCondition);

        Map<String, Object> minMax = dynamicDataSourceManager.getGenericDao(config.getDataSourceId()).queryForMap(minMaxSql);
        Object minValue = startValue != null ? startValue : minMax.get("min_val");
        Object maxValue = minMax.get("max_val");

        if (minValue == null || maxValue == null) {
            return Collections.emptyList();
        }

        // 根据数据类型计算范围
        return calculateRangesByDataType(minValue, maxValue, threadCount, primaryKey);
    }

    /**
     * 根据数据类型计算范围
     */
    private List<SyncRange> calculateRangesByDataType(Object minValue, Object maxValue,
                                                      int threadCount, String primaryKey) {
        List<SyncRange> ranges = new ArrayList<>();

        if (minValue instanceof Number && maxValue instanceof Number) {
            // 数值类型主键
            long min = ((Number) minValue).longValue();
            long max = ((Number) maxValue).longValue();
            long rangeSize = (max - min) / threadCount + 1;

            for (int i = 0; i < threadCount; i++) {
                long start = min + i * rangeSize;
                long end = Math.min(start + rangeSize - 1, max);

                if (start <= max) {
                    ranges.add(new SyncRange(start, end, primaryKey));
                }
            }
        } else {
            // 字符串或其他类型主键，使用基于排序的分页
            // 这种情况下只能单线程处理，或者使用更复杂的分区策略
            ranges.add(new SyncRange(minValue, maxValue, primaryKey));
        }

        return ranges;
    }


    /**
     * 保存检查点
     */
    private void saveCheckpoint(SyncProgress progress, Object lastValue) {
        try {
            progress.setLastSyncValue(lastValue);
            progress.setLastUpdateTime(LocalDateTime.now());
            progressRepository.save(progress);
        } catch (Exception e) {
            log.error("[binlog-sync] Failed to save checkpoint for task: {}", progress.getTaskId(), e);
        }
    }

    /**
     * 获取或创建同步进度
     */
    // 不再使用表级聚合进度记录

    /**
     * 获取或创建线程级同步进度
     */
    private SyncProgress getOrCreateThreadProgress(String taskId, String tableName, int threadIndex) {
        SyncProgress tp = progressRepository.findByTaskIdAndTableNameAndThreadIndex(taskId, tableName, threadIndex);
        if (tp == null) {
            tp = new SyncProgress();
            tp.setTaskId(taskId);
            tp.setTableName(tableName);
            tp.setThreadIndex(threadIndex);
            tp.setSyncType(SyncType.FULL);
            tp.setStatus(SyncStatus.RUNNING);
            tp.setTotalRecords(0);
            tp.setProcessedRecords(0);
            tp.setSuccessRecords(0);
            tp.setFailedRecords(0);
            tp.setStartTime(LocalDateTime.now());
            tp.setMetadata(new HashMap<>());
            progressRepository.save(tp);
        }
        return tp;
    }

    private void ensureThreadProgressExists(String taskId, String tableName, int threadIndex) {
        SyncProgress existing = progressRepository.findByTaskIdAndTableNameAndThreadIndex(taskId, tableName, threadIndex);
        if (existing == null) {
            SyncProgress tp = new SyncProgress();
            tp.setTaskId(taskId);
            tp.setTableName(tableName);
            tp.setThreadIndex(threadIndex);
            tp.setSyncType(SyncType.FULL);
            tp.setStatus(SyncStatus.RUNNING);
            tp.setTotalRecords(0);
            tp.setProcessedRecords(0);
            tp.setSuccessRecords(0);
            tp.setFailedRecords(0);
            tp.setStartTime(LocalDateTime.now());
            tp.setLastUpdateTime(LocalDateTime.now());
            tp.setMetadata(new HashMap<>());
            progressRepository.save(tp);
        }
    }

    /**
     * 计算总记录数
     */
    private long countTotalRecords(SyncTaskConfig config, String tableName, String whereCondition) {
        String countSql = GenericDao.buildCountSql(tableName, whereCondition);
        return dynamicDataSourceManager.getGenericDao(config.getDataSourceId()).queryForCount(countSql);
    }


    private void updateSyncStatus(String taskId, SyncStatus status) {
        updateSyncStatus(taskId, status, null);
    }

    private void updateSyncStatus(String taskId, SyncStatus status, String errorMessage) {
        try {
            List<SyncProgress> progresses = progressRepository.findAllByTaskId(taskId);
            for (SyncProgress p : progresses) {
                p.setStatus(status);
                p.setLastUpdateTime(LocalDateTime.now());
                if (errorMessage != null) {
                    p.setErrorMessage(errorMessage);
                }
                progressRepository.save(p);
            }
        } catch (Exception e) {
            log.error("[binlog-sync] Failed to update sync status for task: {}", taskId, e);
        }
    }


    private boolean shouldStopOnError(SyncTaskConfig config) {
        // 可以从配置中读取错误处理策略
        return config.getFullSyncConfig() != null && config.getFullSyncConfig().isStopOnError();
    }

}
