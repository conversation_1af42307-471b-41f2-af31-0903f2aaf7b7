package com.xyy.saas.binlog.config.detail;


import lombok.Data;

/**
 * desc 全量同步配置
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class FullSyncConfig {
    private boolean enabled = false; // 是否启用全量同步
    private String taskName;
    private int batchSize = 1000; // 批次大小
    private int threadCount = 4; // 并发线程数
    private boolean singleThread = false; // 是否单线程
    private boolean syncWithMaxId = false; // 根据最大id分页同步开关
    private int threadSleepDelay = 500; // 每批线程执行休眠ms
    private String whereCondition; // 自定义过滤条件
    private String orderBy; // 排序字段，用于断点恢复
    private boolean enableCheckpoint = true; // 是否启用断点恢复
    private long checkpointInterval = 10000; // 检查点间隔（毫秒）
    private boolean stopOnError = false; // 遇到错误时是否停止全量同步（默认不中断）
}
