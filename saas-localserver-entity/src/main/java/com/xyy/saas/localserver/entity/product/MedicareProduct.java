package com.xyy.saas.localserver.entity.product;

import com.xyy.saas.datasync.client.constants.DataSyncDirection;
import com.xyy.saas.datasync.client.entity.AutoIncrement;
import com.xyy.saas.datasync.client.entity.DataSyncEntity;
import com.xyy.saas.datasync.client.entity.PrimaryKey;
import jakarta.validation.constraints.NotNull;

import lombok.Data;

/**
 * @Desc 医保商品
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/04/23 14:40
 */
@Data
@DataSyncEntity(direction = DataSyncDirection.LOCAL_TO_CLOUD)
public class MedicareProduct {

  @PrimaryKey
  @AutoIncrement
  private Integer id;

  @NotNull
  private String name;

  /**
   * 通用名
   */
  @NotNull
  private String commonName;

}
