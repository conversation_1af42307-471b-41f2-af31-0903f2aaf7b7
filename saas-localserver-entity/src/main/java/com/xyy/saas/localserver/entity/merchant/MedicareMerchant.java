package com.xyy.saas.localserver.entity.merchant;

import com.xyy.saas.datasync.client.constants.DataSyncDirection;
import com.xyy.saas.datasync.client.entity.AutoIncrement;
import com.xyy.saas.datasync.client.entity.DataSyncEntity;
import com.xyy.saas.datasync.client.entity.PrimaryKey;
import jakarta.validation.constraints.NotNull;
import org.hibernate.validator.constraints.Length;

import lombok.Data;

/**
 * @Desc 医保商户
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/04/23 14:49
 */
@Data
@DataSyncEntity(direction = DataSyncDirection.LOCAL_TO_CLOUD)
public class MedicareMerchant {

  @PrimaryKey
  @AutoIncrement
  private Integer id;

  /**
   * 两定机构编码
   */
  @Length(min = 11, max = 15)
  private String medicareInstitutionCode;

  /**
   * 两定机构名称
   */
  @Length(min = 4, max = 100)
  private String medicareInstitutionName;

  /**
   * 机构号
   */
  @NotNull
  private String organSign;

}
