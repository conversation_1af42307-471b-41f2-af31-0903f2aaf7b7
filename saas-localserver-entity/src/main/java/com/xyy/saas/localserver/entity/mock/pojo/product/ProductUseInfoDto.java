package com.xyy.saas.localserver.entity.mock.pojo.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 商品使用信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProductUseInfoDto {

    @Schema(description = "商品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String productPref;

    @Schema(description = "租户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "18520")
    private Long tenantId;

    @Schema(description = "租户编号（总部）", requiredMode = Schema.RequiredMode.REQUIRED, example = "11537")
    private Long headTenantId;

    @Schema(description = "零售价", example = "19488")
    private BigDecimal retailPrice;

    @Schema(description = "会员价", example = "16302")
    private BigDecimal memberPrice;

    @Schema(description = "医保项目编码")
    private String medicareProjectCode;

    @Schema(description = "医保项目名称", example = "赵六")
    private String medicareProjectName;

    @Schema(description = "医保项目等级", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer medicareProjectLevel;

    @Schema(description = "医保匹配状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer medicareMatchStatus;

    @Schema(description = "匹配关系上传状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer medicareUploadStatus;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你猜")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

}