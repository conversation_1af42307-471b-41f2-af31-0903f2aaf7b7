package com.xyy.saas.localserver.entity.mock;

import com.xyy.saas.datasync.client.constants.DataContextHolder;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.localserver.entity.mock.pojo.product.ProductUseInfoDto;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

public class MockProductUtil {
    // 内存存储结构：租户ID -> 商品列表
    private static final ConcurrentHashMap<Long, List<ProductInfoDto>> productStore = new ConcurrentHashMap<>();

    static {
        // 初始化总部商品
        List<ProductInfoDto> headProducts = new ArrayList<>();
        for (int i = 1; i <= 20; i++) {
            headProducts.add(createHeadProduct(i));
        }
        productStore.put(20000L, headProducts); // 假设总部ID为20000

        // 初始化单体门店商品
        List<ProductInfoDto> singleProducts = new ArrayList<>();
        for (int i = 1; i <= 20; i++) {
            singleProducts.add(createSingleProduct(i));
        }
        productStore.put(10001L, singleProducts); // 假设单体门店ID为10001
    }

    private static ProductInfoDto createHeadProduct(int index) {
        return ProductInfoDto.builder()
                .id(20000L * 1000 + index)
                .tenantId(20000L)
                .pref("HQ-PROD-" + index)
                .commonName("总部商品" + index)
                .brandName("康泰医药")
                .spec("10mg*" + index + "片")
                .barcode("692123456789" + String.format("%02d", index))
                .manufacturer("康泰制药总厂")
                .approvalNumber("国药准字Z202300" + index)
                .unit("1001")
                .inputTaxRate(new BigDecimal("0.13"))
                .outputTaxRate(new BigDecimal("0.13"))
                .createTime(LocalDateTime.now())
                .mnemonicCode(buildMnemonicCode(index))
                .stdlibId(createMockStdlib(index, true))
//                .useInfo(createMockUseInfo(index, true))
                .build();
    }

    private static ProductInfoDto createSingleProduct(int index) {
        return ProductInfoDto.builder()
                .id(10001L * 1000 + index)
                .tenantId(10001L)
                .pref("STORE-PROD-" + index)
                .commonName("门店商品" + index)
                .brandName("阳光大药房")
                .spec("5mg*" + (index * 2) + "粒")
                .barcode("693123456789" + String.format("%02d", index))
                .manufacturer("阳光制药")
                .approvalNumber("国药准字H202300" + index)
                .unit("1002")
                .inputTaxRate(new BigDecimal("0.10"))
                .outputTaxRate(new BigDecimal("0.10"))
                .createTime(LocalDateTime.now())
                .mnemonicCode(buildMnemonicCode(index))
                .stdlibId(createMockStdlib(index, false))
//                .useInfo(createMockUseInfo(index, false))
                .build();
    }

    private static String buildMnemonicCode(int index) {
        return String.format("HQ-PROD-%d|康泰医药|总部商品%d|10mg*%d片|692123456789%02d|20000|国药准字Z202300%d",
                index, index, index, index, index);
    }

    private static Long createMockStdlib(int index, boolean isHead) {
        return isHead ? 200000L + index : 100000L + index;
    }

    private static ProductUseInfoDto createMockUseInfo(int index, boolean isHead) {
        return ProductUseInfoDto.builder()
                .retailPrice(isHead ? new BigDecimal("25.00") : new BigDecimal("28.00"))
                .medicareProjectLevel(1)
                .medicareProjectCode("YB2023" + index)
                .medicareProjectName(isHead ? "康泰项目" + index : "阳光项目" + index)
                .build();
    }

    /**
     * 根据租户ID和助记码查询商品列表
     */
    public static List<ProductInfoDto> getProducts(Long tenantId, String keyword) {
        return productStore.getOrDefault(tenantId, new ArrayList<>())
                .stream()
                .filter(p -> p.getMnemonicCode().toLowerCase().contains(keyword.toLowerCase()))
                .collect(Collectors.toList());
    }

    /**
     * 根据租户ID和商品内码查询单个商品
     */
    public static ProductInfoDto getProductByPref(Long tenantId, String productPref) {
        return productStore.getOrDefault(tenantId, new ArrayList<>())
                .stream()
                .filter(p -> p.getPref().equals(productPref))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据租户ID和商品内码列表查询商品map
     */
    public static Map<String, ProductInfoDto> getProductByPrefs(Long tenantId, List<String> productPrefs) {
        return productStore.getOrDefault(tenantId, new ArrayList<>())
                .stream()
                .filter(p -> productPrefs.contains(p.getPref()))
                .collect(Collectors.toMap(ProductInfoDto::getPref, p -> p, (k1, k2) -> k1));
    }

    /**
     * 根据租户ID和商品内码列表查询商品map
     */
    public static List<ProductInfoDto> listProductInfoByPref(List<String> productPrefs) {
        return productStore.getOrDefault(Long.valueOf(DataContextHolder.getTenantId()), new ArrayList<>())
                .stream()
                .filter(p -> productPrefs.contains(p.getPref())).toList();
    }

}
