package com.xyy.saas.localserver.purchase.server.admin.stockout;

import com.xyy.saas.localserver.purchase.api.stockout.dto.StockoutBillDetailDTO;
import com.xyy.saas.localserver.purchase.api.stockout.dto.StockoutBillDetailPageReqDTO;
import com.xyy.saas.localserver.purchase.server.admin.stockout.vo.StockoutBillDetailPageReqVO;
import com.xyy.saas.localserver.purchase.server.admin.stockout.vo.StockoutBillDetailRespVO;
import com.xyy.saas.localserver.purchase.server.admin.stockout.vo.StockoutBillDetailSaveReqVO;
import com.xyy.saas.localserver.purchase.server.convert.stockout.StockoutBillDetailConvert;
import com.xyy.saas.localserver.purchase.server.service.stockout.StockoutBillDetailService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

/**
 * 管理后台 - （总部）缺货单明细信息 Controller
 * 处理缺货单明细的创建、更新、删除、查询等操作
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "管理后台 - （总部）缺货单明细信息")
@RestController
@RequestMapping("/saas/purchase/stockout-bill-detail")
@Validated
public class StockoutBillDetailController {

    @Resource
    private StockoutBillDetailService stockoutBillDetailService;

    /**
     * 创建（总部）缺货单明细信息
     * 处理流程：
     * 1. 对象转换：将VO对象转换为DTO对象
     * 2. 调用服务：执行创建操作
     *
     * @param createReqVO 创建信息
     * @return 创建结果
     */
    @PostMapping("/create")
    @Operation(summary = "创建（总部）缺货单明细信息")
    @PreAuthorize("@ss.hasPermission('saas:stockout-bill-detail:create')")
    public CommonResult<Long> createStockoutBillDetail(@Valid @RequestBody StockoutBillDetailSaveReqVO createReqVO) {
        // 1. 对象转换：将VO对象转换为DTO对象
        StockoutBillDetailDTO createDTO = StockoutBillDetailConvert.INSTANCE.convert2DTO(createReqVO);
        // 2. 调用服务：执行创建操作
        return success(stockoutBillDetailService.createStockoutBillDetail(createDTO));
    }

    /**
     * 更新（总部）缺货单明细信息
     * 处理流程：
     * 1. 对象转换：将VO对象转换为DTO对象
     * 2. 调用服务：执行更新操作
     *
     * @param updateReqVO 更新信息
     * @return 更新结果
     */
    @PutMapping("/update")
    @Operation(summary = "更新（总部）缺货单明细信息")
    @PreAuthorize("@ss.hasPermission('saas:stockout-bill-detail:update')")
    public CommonResult<Boolean> updateStockoutBillDetail(@Valid @RequestBody StockoutBillDetailSaveReqVO updateReqVO) {
        // 1. 对象转换：将VO对象转换为DTO对象
        StockoutBillDetailDTO updateDTO = StockoutBillDetailConvert.INSTANCE.convert2DTO(updateReqVO);
        // 2. 调用服务：执行更新操作
        stockoutBillDetailService.updateStockoutBillDetail(updateDTO);
        return success(true);
    }

    /**
     * 删除（总部）缺货单明细信息
     * 处理流程：
     * 1. 调用服务：执行删除操作
     *
     * @param id 编号
     * @return 删除结果
     */
    @DeleteMapping("/delete")
    @Operation(summary = "删除（总部）缺货单明细信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:stockout-bill-detail:delete')")
    public CommonResult<Boolean> deleteStockoutBillDetail(@RequestParam("id") Long id) {
        // 调用服务：执行删除操作
        stockoutBillDetailService.deleteStockoutBillDetail(id);
        return success(true);
    }

    /**
     * 获取（总部）缺货单明细信息
     * 处理流程：
     * 1. 调用服务：获取缺货单明细信息
     * 2. 对象转换：将DTO对象转换为VO对象
     *
     * @param id 编号
     * @return 缺货单明细信息
     */
    @GetMapping("/get")
    @Operation(summary = "获得（总部）缺货单明细信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('saas:stockout-bill-detail:query')")
    public CommonResult<StockoutBillDetailRespVO> getStockoutBillDetail(@RequestParam("id") Long id) {
        // 1. 调用服务：获取缺货单明细信息
        StockoutBillDetailDTO stockoutBillDetail = stockoutBillDetailService.getStockoutBillDetail(id);
        // 2. 对象转换：将DTO对象转换为VO对象
        return success(StockoutBillDetailConvert.INSTANCE.convert2VO(stockoutBillDetail));
    }

    /**
     * 获取（总部）缺货单明细信息分页
     * 处理流程：
     * 1. 对象转换：将VO对象转换为DTO对象
     * 2. 调用服务：获取分页数据
     * 3. 对象转换：将DTO对象转换为VO对象
     *
     * @param pageReqVO 分页查询参数
     * @return 分页结果
     */
    @GetMapping("/page")
    @Operation(summary = "获得（总部）缺货单明细信息分页")
    @PreAuthorize("@ss.hasPermission('saas:stockout-bill-detail:query')")
    public CommonResult<PageResult<StockoutBillDetailRespVO>> getStockoutBillDetailPage(
            @Valid StockoutBillDetailPageReqVO pageReqVO) {
        // 1. 对象转换：将VO对象转换为DTO对象
        StockoutBillDetailPageReqDTO pageReqDTO = StockoutBillDetailConvert.INSTANCE.convert2DTO(pageReqVO);
        // 2. 调用服务：获取分页数据
        PageResult<StockoutBillDetailDTO> pageResult = stockoutBillDetailService.getStockoutBillDetailPage(pageReqDTO);
        // 3. 对象转换：将DTO对象转换为VO对象
        return success(StockoutBillDetailConvert.INSTANCE.convert2VO(pageResult));
    }

    /**
     * 导出（总部）缺货单明细信息Excel
     * 处理流程：
     * 1. 设置分页大小：设置为不分页
     * 2. 对象转换：将VO对象转换为DTO对象
     * 3. 调用服务：获取数据列表
     * 4. 导出Excel：将数据导出为Excel文件
     *
     * @param pageReqVO 分页查询参数
     * @param response  HTTP响应对象
     * @throws IOException 导出过程中的IO异常
     */
    @GetMapping("/export-excel")
    @Operation(summary = "导出（总部）缺货单明细信息 Excel")
    @PreAuthorize("@ss.hasPermission('saas:stockout-bill-detail:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportStockoutBillDetailExcel(@Valid StockoutBillDetailPageReqVO pageReqVO,
            HttpServletResponse response) throws IOException {
        // 1. 设置分页大小：设置为不分页
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        // 2. 对象转换：将VO对象转换为DTO对象
        StockoutBillDetailPageReqDTO pageReqDTO = StockoutBillDetailConvert.INSTANCE.convert2DTO(pageReqVO);
        // 3. 调用服务：获取数据列表
        List<StockoutBillDetailDTO> list = stockoutBillDetailService.getStockoutBillDetailPage(pageReqDTO).getList();
        // 4. 导出Excel：将数据导出为Excel文件
        ExcelUtils.write(response, "（总部）缺货单明细信息.xls", "数据", StockoutBillDetailRespVO.class,
                StockoutBillDetailConvert.INSTANCE.convert2VOList(list));
    }
}