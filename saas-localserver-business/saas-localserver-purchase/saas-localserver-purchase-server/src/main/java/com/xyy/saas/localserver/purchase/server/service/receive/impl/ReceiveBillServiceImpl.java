package com.xyy.saas.localserver.purchase.server.service.receive.impl;

import cn.hutool.core.lang.Assert;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.localserver.inventory.api.stock.InventoryStockApi;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventoryIncreaseDTO;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillDTO;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillDetailDTO;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDTO;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDetailDTO;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillPageReqDTO;
import com.xyy.saas.localserver.purchase.enums.MedicineTypeEnum;
import com.xyy.saas.localserver.purchase.enums.receive.ReceiveBillStatusEnum;
import com.xyy.saas.localserver.purchase.enums.receive.ReceiveBillTypeEnum;
import com.xyy.saas.localserver.purchase.server.admin.purchase.vo.PurchaseBillSaveReqVO;
import com.xyy.saas.localserver.purchase.server.convert.extend.PurchaseInvoiceConvert;
import com.xyy.saas.localserver.purchase.server.convert.extend.PurchaseTransportConvert;
import com.xyy.saas.localserver.purchase.server.convert.purchase.PurchaseBillConvert;
import com.xyy.saas.localserver.purchase.server.convert.receive.ReceiveBillConvert;
import com.xyy.saas.localserver.purchase.server.convert.receive.ReceiveBillDetailConvert;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.extend.PurchaseInvoiceDO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.extend.PurchaseTransportDO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.purchase.PurchaseBillDO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.receive.ReceiveBillDO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.receive.ReceiveBillDetailDO;
import com.xyy.saas.localserver.purchase.server.dal.mysql.extend.PurchaseInvoiceMapper;
import com.xyy.saas.localserver.purchase.server.dal.mysql.extend.PurchaseTransportMapper;
import com.xyy.saas.localserver.purchase.server.dal.mysql.receive.ReceiveBillMapper;
import com.xyy.saas.localserver.purchase.server.dal.mysql.receive.ReceiveBillDetailMapper;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.localserver.purchase.server.protocol.CloudServiceClient;
import com.xyy.saas.localserver.purchase.server.protocol.purchase.PurchaseClient;
import com.xyy.saas.localserver.purchase.server.service.purchase.PurchaseBillService;
import com.xyy.saas.localserver.purchase.server.service.purchase.PurchaseOrderService;
import com.xyy.saas.localserver.purchase.server.service.receive.ReceiveBillService;
import com.xyy.saas.localserver.purchase.server.service.receive.handler.RejectHandler;
import com.xyy.saas.localserver.purchase.server.service.receive.handler.stockin.StockIncreaseHandlerManager;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.localserver.purchase.enums.ErrorCodeConstants.*;
import static com.xyy.saas.localserver.purchase.enums.receive.ReceiveBillStatusEnum.REJECTED;
import static com.xyy.saas.localserver.purchase.enums.receive.ReceiveBillStatusEnum.STORED;
import com.xyy.saas.localserver.purchase.server.utils.ProductInfoUtil;

/**
 * 收货单 Service 实现类
 *
 * <p>
 * 该实现类负责处理收货单相关的所有业务逻辑，主要包括：
 * </p>
 * <ul>
 * <li>基础操作：收货、验收、入库等</li>
 * <li>特殊操作：一步入库、拒收入库等</li>
 * <li>单据管理：增删改查、状态流转等</li>
 * <li>关联处理：采购单状态更新、库存变更等</li>
 * </ul>
 *
 * <p>
 * 主要特点：
 * </p>
 * <ul>
 * <li>支持多种收货类型：采购收货、调拨收货、退货收货等</li>
 * <li>支持多种租户类型：连锁总部、连锁门店等</li>
 * <li>支持多种业务场景：普通入库、一步入库、拒收入库等</li>
 * <li>支持完整的单据生命周期管理</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
public class ReceiveBillServiceImpl implements ReceiveBillService {

    // ========== 常量定义 ==========

    /** 允许回退的单据状态 */
    private static final Set<Integer> INVALID_ROLLBACK_STATUSES = Stream.of(
            ReceiveBillStatusEnum.STORED,
            REJECTED,
            ReceiveBillStatusEnum.PENDING_RECEIVE).map(ReceiveBillStatusEnum::getCode)
            .collect(Collectors.toUnmodifiableSet());

    /** 中药标识 */
    private static final Set<String> TCM_FLAG = Set.of("中西成药");

    // ========== 依赖注入 ==========

    @Resource
    private PurchaseOrderService purchaseOrderService;
    @Resource
    private PurchaseBillService purchaseBillService;
    @Resource
    private ReceiveBillMapper receiveBillMapper;
    @Resource
    private ReceiveBillDetailMapper receiveBillDetailMapper;
    @Resource
    private PurchaseTransportMapper purchaseTransportMapper;
    @Resource
    private PurchaseInvoiceMapper purchaseInvoiceMapper;
    @Resource
    private List<RejectHandler> rejectHandlers;
    @Resource
    private CloudServiceClient cloudServiceClient;
    @Resource
    private InventoryStockApi inventoryStockApi;
    @Resource
    private StockIncreaseHandlerManager stockIncreaseHandlerManager;

    // ========== 业务操作 ==========

    /**
     * 收货操作
     * 处理流程：
     * 1. 填充商品信息：为明细中的商品补充完整信息
     * 2. 填充收货内容：设置收货单的基本信息
     * 3. 保存单据：将收货单信息持久化到数据库
     * 4. 判断是否提交：
     * 4.1 处理拒收情况：检查并处理可能的拒收场景
     * 4.2 更新采购单状态：同步更新关联的采购单状态
     * 4.3 更新追溯码：调用库存服务更新商品追溯信息
     *
     * @param receiveBill 收货单信息
     * @return 收货单编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long receive(ReceiveBillDTO receiveBill) {
        // 1. 填充商品信息：为明细中的商品补充完整信息
        ReceiveBillDetailConvert.INSTANCE.fillProductInfo(receiveBill.getDetails());

        // 2. 填充收货内容：设置收货单的基本信息
        ReceiveBillConvert.INSTANCE.fillReceiveContent(receiveBill);

        // 3. 保存单据：将收货单信息持久化到数据库
        saveReceiveBill(receiveBill);

        // 4. 判断是否提交
        if (receiveBill.getSubmitted()) {
            // 4.1 处理拒收情况：检查并处理可能的拒收场景
            handleReject(receiveBill);

            // 4.2 更新采购单状态：同步更新关联的采购单状态
            updatePurchaseBillAfterReceive(receiveBill);

            // 4.3 todo 调用库存服务，更新追溯码
        }

        return receiveBill.getId();
    }

    /**
     * 验收操作
     * 处理流程：
     * 1. 校验收货单存在：确保要验收的单据存在
     * 2. 保存单据：将验收信息持久化到数据库
     *
     * @param receiveBill 验收单信息
     * @return 验收单编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long accept(ReceiveBillDTO receiveBill) {
        // 1. 校验收货单存在：确保要验收的单据存在
        getReceiveBill(receiveBill.getId());

        // 2. 保存单据：将验收信息持久化到数据库
        saveReceiveBill(receiveBill);

        return receiveBill.getId();
    }

    /**
     * 入库操作
     * 处理流程：
     * 1. 校验验收单存在：确保要入库的单据已经验收
     * 2. 保存单据：将入库信息持久化到数据库
     * 3. 更新采购单状态：同步更新关联的采购单状态和可退数量
     * 4. 判断是否提交：
     * 4.1 更新库存：调用库存服务增加库存
     * 4.2 处理拒收：检查并处理可能的拒收场景
     *
     * @param receiveBill 入库单信息
     * @return 入库单编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long warehousing(ReceiveBillDTO receiveBill) {
        // 1. 校验验收单存在：确保要入库的单据已经验收
        getReceiveBill(receiveBill.getId());

        // 2. 保存单据：将入库信息持久化到数据库
        saveReceiveBill(receiveBill);

        // 3. 更新采购单状态：同步更新关联的采购单状态和可退数量
        updatePurchaseBillAfterReceive(receiveBill);

        // 4. 判断是否提交
        if (receiveBill.getSubmitted()) {
            // 4.1 更新库存：调用库存服务增加库存
            InventoryIncreaseDTO inventoryIncreaseDTO = stockIncreaseHandlerManager
                    .processStockInParams(receiveBill);
            inventoryStockApi.increaseStock(inventoryIncreaseDTO);

            // 4.2 处理拒收：检查并处理可能的拒收场景
            handleReject(receiveBill);
        }

        return receiveBill.getId();
    }

    /**
     * 回退操作
     * 处理流程：
     * 1. 校验单据存在：确保要回退的单据存在
     * 2. 校验单据状态：检查单据状态是否允许回退
     * 3. 转换回滚对象：将当前单据转换为回滚状态
     * 4. 执行回退：更新单据状态
     * 5. 处理追溯码：如果回退到收货状态，需要处理追溯码
     *
     * @param id 要回退的单据编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rollback(Long id) {
        // 1. 校验单据存在：确保要回退的单据存在
        ReceiveBillDTO receiveBillDTO = getReceiveBill(id);

        // 2. 校验单据状态：检查单据状态是否允许回退
        validateRollbackStatus(receiveBillDTO);

        // 3. 转换回滚对象：将当前单据转换为回滚状态
        ReceiveBillDO receiveBill = ReceiveBillConvert.INSTANCE.convert2RollbackDO(receiveBillDTO);

        // 4. 执行回退：更新单据状态
        receiveBillMapper.updateById(receiveBill);

        // 5. 处理追溯码：如果回退到收货状态，需要处理追溯码
        if (Objects.equals(ReceiveBillStatusEnum.PENDING_RECEIVE.getCode(), receiveBill.getStatus())) {
            // TODO: 调用库存服务，删除（回退）追溯码&追溯码账页
        }
    }

    /**
     * 一步入库操作
     * 处理流程：
     * 1. 填充商品信息：为明细中的商品补充完整信息
     * 2. 拆分单据：按照中药/西药类型拆分成多个单据
     * 3. 处理拆分单据：
     * 3.1 填充收货内容：设置每个拆分单据的基本信息
     * 3.2 组装采购单：为每个拆分单据生成对应的采购单
     * 3.3 组装库存参数：准备库存增加的参数
     * 4. 批量保存：保存所有拆分后的单据
     * 5. 保存采购单：保存所有生成的采购单
     * 6. 更新库存：调用库存服务批量增加库存
     *
     * @param receiveBill 入库单信息
     * @return 拆分后的单据数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer oneStepWarehousing(ReceiveBillDTO receiveBill) {
        // 1. 填充商品信息：为明细中的商品补充完整信息
        ReceiveBillDetailConvert.INSTANCE.fillProductInfo(receiveBill.getDetails());

        // 2. 拆分单据：按照中药/西药类型拆分成多个单据
        List<ReceiveBillDTO> splitBills = splitReceiveBill(receiveBill);

        // 3. 处理拆分单据
        List<PurchaseBillDTO> purchaseBills = new ArrayList<>();
        List<InventoryIncreaseDTO> inventoryIncreases = new ArrayList<>();

        for (ReceiveBillDTO bill : splitBills) {
            // 3.1 填充收货内容：设置每个拆分单据的基本信息
            ReceiveBillConvert.INSTANCE.fillReceiveContent(bill);

            // 3.2 组装采购单：为每个拆分单据生成对应的采购单
            PurchaseBillDTO purchaseBill = ReceiveBillConvert.INSTANCE.generatePurchaseOrder(bill);
            purchaseBills.add(purchaseBill);

            // 3.3 组装库存参数：准备库存增加的参数
            InventoryIncreaseDTO inventoryIncrease = stockIncreaseHandlerManager.processStockInParams(bill);
            inventoryIncreases.add(inventoryIncrease);
        }

        // 4. 批量保存：保存所有拆分后的单据
        batchSaveReceiveBills(splitBills);

        // 5. 保存采购单：保存所有生成的采购单
        purchaseBills.forEach(purchaseBill -> purchaseOrderService.savePurchaseOrder(purchaseBill));

        // 6. 更新库存：调用库存服务批量增加库存
        inventoryStockApi.batchIncreaseStock(inventoryIncreases);

        return splitBills.size();
    }

    /**
     * 拒收入库操作
     * 处理流程：
     * 1. 填充商品信息：为明细中的商品补充完整信息
     * 2. 填充收货内容：设置收货单的基本信息
     * 3. 查询默认人员：获取默认的收货、验收、入库员信息
     * 4. 查询默认货位：获取门店/总部默认待处理货位
     * 5. 更新库存：调用库存服务增加库存
     * 6. 保存单据：将拒收入库信息持久化到数据库
     *
     * @param receiveBill 拒收入库单信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rejectWarehousing(ReceiveBillDTO receiveBill) {
        // 1. 填充商品信息：为明细中的商品补充完整信息
        ReceiveBillDetailConvert.INSTANCE.fillProductInfo(receiveBill.getDetails());

        // 2. 填充收货内容：设置收货单的基本信息
        ReceiveBillConvert.INSTANCE.fillReceiveContent(receiveBill);

        // 3. 查询默认人员：获取默认的收货、验收、入库员信息
        // TODO: 查询默认收货、验收、入库员

        // 4. 查询默认货位：获取门店/总部默认待处理货位
        // TODO: 查询门店/总部默认待处理货位

        // 5. 更新库存：调用库存服务增加库存
        InventoryIncreaseDTO inventoryIncreaseDTO = stockIncreaseHandlerManager.processStockInParams(receiveBill);
        inventoryStockApi.increaseStock(inventoryIncreaseDTO);

        // 6. 保存单据：将拒收入库信息持久化到数据库
        saveReceiveBill(receiveBill);
    }

    // ========== 基础操作 ==========

    /**
     * 保存收货单
     * 处理流程：
     * 1. 主表操作：
     * 1.1 如果单据已存在，则更新
     * 1.2 如果单据不存在，则新增
     * 2. 明细处理：保存单据明细信息
     *
     * @param receiveBill 要保存的收货单信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveReceiveBill(ReceiveBillDTO receiveBill) {
        // 1. 主表操作
        if (receiveBill.getId() != null) {
            // 1.1 如果单据已存在，则更新
            updateExistingBill(receiveBill);
        } else {
            // 1.2 如果单据不存在，则新增
            insertNewBill(receiveBill);
        }

        // 2. 明细处理：保存单据明细信息
        handleBillDetails(receiveBill);
    }

    /**
     * 删除收货单
     * 处理流程：
     * 1. 校验单据存在：确保要删除的单据存在
     * 2. 删除明细：删除单据关联的明细信息
     * 3. 删除运输信息：删除单据关联的运输信息
     * 4. 删除发票信息：删除单据关联的发票信息
     * 5. 删除主表：删除单据主表信息
     *
     * @param id 要删除的单据编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteReceiveBill(Long id) {
        // 1. 校验单据存在：确保要删除的单据存在
        ReceiveBillDTO receiveBill = getReceiveBill(id);

        // 2. 删除明细：删除单据关联的明细信息
        receiveBillDetailMapper.deleteByBillNoAndTenant(receiveBill.getBillNo(), receiveBill.getTenantId());

        // 3. 删除运输信息：删除单据关联的运输信息
        purchaseTransportMapper.deleteByBillNo(receiveBill.getBillNo());

        // 4. 删除发票信息：删除单据关联的发票信息
        purchaseInvoiceMapper.deleteByBillNo(receiveBill.getBillNo());

        // 5. 删除主表：删除单据主表信息
        receiveBillMapper.deleteById(id);
    }

    /**
     * 获取收货单
     * 处理流程：
     * 1. 查询单据：根据ID查询单据信息
     * 2. 校验存在：确保单据存在
     * 3. 转换返回：将DO对象转换为DTO对象
     *
     * @param id 单据编号
     * @return 收货单信息
     */
    @Override
    public ReceiveBillDTO getReceiveBill(Long id) {
        // 1. 查询单据：根据ID查询单据信息
        ReceiveBillDO receiveBill = receiveBillMapper.selectById(id);
        // 2. 校验存在：确保单据存在
        Assert.notNull(receiveBill, RECEIVE_BILL_NOT_EXISTS.getMsg());
        // 3. 转换返回：将DO对象转换为DTO对象
        return ReceiveBillConvert.INSTANCE.convert2DTO(receiveBill);
    }

    /**
     * 获取收货单及详情
     * 处理流程：
     * 1. 查询单据：根据单号和租户ID查询单据及详情
     * 2. 校验存在：确保单据存在
     * 3. 填充商品信息：为明细中的商品补充完整信息
     *
     * @param billNo   单据编号
     * @param tenantId 租户编号
     * @return 收货单及详情信息
     */
    @Override
    public ReceiveBillDTO getReceiveBillAndDetails(String billNo, Long tenantId) {
        // 1. 查询单据：根据单号和租户ID查询单据及详情
        ReceiveBillDTO receiveBill = receiveBillMapper.getReceiveBillWithDetails(billNo, tenantId);
        // 2. 校验存在：确保单据存在
        Assert.notNull(receiveBill, RECEIVE_BILL_NOT_EXISTS.getMsg());

        // 3. 填充商品信息：为明细中的商品补充完整信息
        ProductInfoUtil.fillProductInfo(
                receiveBill.getDetails(),
                ReceiveBillDetailDTO::getProductPref,
                ReceiveBillDetailDTO::setProductInfo);

        return receiveBill;
    }

    /**
     * 获取收货单分页
     * 处理流程：
     * 1. 查询分页：根据查询条件获取分页数据
     * 2. 转换结果：将DO对象转换为DTO对象
     * 3. 补充信息：补充审批流、操作员等信息
     *
     * @param pageReqDTO 分页查询条件
     * @return 分页结果
     */
    @Override
    public PageResult<ReceiveBillDTO> getReceiveBillPage(ReceiveBillPageReqDTO pageReqDTO) {
        // 1. 查询分页：根据查询条件获取分页数据
        PageResult<ReceiveBillDO> receiveBills = receiveBillMapper.selectPage(pageReqDTO);
        // 2. 转换结果：将DO对象转换为DTO对象
        PageResult<ReceiveBillDTO> pageResult = ReceiveBillConvert.INSTANCE.convert2DTO(receiveBills);

        // 3. 补充信息：补充审批流、操作员等信息
        // TODO: 组装审批流、操作员等信息
        return pageResult;
    }

    // ========== 私有方法 ==========

    /**
     * 批量保存收货单
     * 处理流程：
     * 1. 分离单据：将单据分为需要更新和需要插入的两组
     * 2. 处理已存在单据：
     * 2.1 批量更新主表
     * 2.2 删除原有明细
     * 3. 处理新单据：批量插入主表
     * 4. 处理明细：批量插入所有单据的明细
     *
     * @param receiveBills 待处理的收货单列表
     */
    private void batchSaveReceiveBills(List<ReceiveBillDTO> receiveBills) {
        if (CollectionUtils.isEmpty(receiveBills)) {
            return;
        }

        // 1. 分离需要更新和插入的单据
        Map<Boolean, List<ReceiveBillDTO>> billsByOperation = separateBillsByOperation(receiveBills);

        // 2. 处理已存在的单据
        handleExistingBills(billsByOperation.getOrDefault(true, Collections.emptyList()));

        // 3. 处理新单据
        handleNewBills(billsByOperation.getOrDefault(false, Collections.emptyList()));

        // 4. 批量处理所有单据的明细
        handleAllBillDetails(receiveBills);
    }

    /**
     * 分离需要更新和插入的单据
     * 根据单据ID是否存在，将单据分为两组：
     * - true: 需要更新的已存在单据
     * - false: 需要插入的新单据
     *
     * @param receiveBills 待分离的收货单列表
     * @return 按操作类型分组的单据Map
     */
    private Map<Boolean, List<ReceiveBillDTO>> separateBillsByOperation(List<ReceiveBillDTO> receiveBills) {
        return receiveBills.stream()
                .collect(Collectors.groupingBy(bill -> bill.getId() != null));
    }

    /**
     * 处理已存在的单据
     * 处理流程：
     * 1. 批量更新主表信息
     * 2. 更新关联的运输信息
     * 3. 更新关联的发票信息
     * 4. 删除原有的明细信息
     *
     * @param existingBills 需要更新的已存在单据列表
     */
    private void handleExistingBills(List<ReceiveBillDTO> existingBills) {
        if (CollectionUtils.isEmpty(existingBills)) {
            return;
        }

        // 1. 批量更新主表
        receiveBillMapper.updateBatch(ReceiveBillConvert.INSTANCE.convert2DOList(existingBills));

        // 2. 批量更新运输信息
        updateTransports(existingBills);

        // 3. 批量更新发票信息
        updateInvoices(existingBills);

        // 4. 删除原有明细
        deleteExistingDetails(existingBills);
    }

    /**
     * 处理新单据
     * 处理流程：
     * 1. 批量插入主表信息
     * 2. 插入关联的运输信息
     * 3. 插入关联的发票信息
     *
     * @param newBills 需要插入的新单据列表
     */
    private void handleNewBills(List<ReceiveBillDTO> newBills) {
        if (CollectionUtils.isEmpty(newBills)) {
            return;
        }

        // 1. 批量插入主表
        receiveBillMapper.insertBatch(ReceiveBillConvert.INSTANCE.convert2DOList(newBills));

        // 2. 批量插入运输信息
        insertTransports(newBills);

        // 3. 批量插入发票信息
        insertInvoices(newBills);
    }

    /**
     * 更新运输信息
     * 从单据中提取运输信息并批量更新
     *
     * @param bills 包含运输信息的单据列表
     */
    private void updateTransports(List<ReceiveBillDTO> bills) {
        List<PurchaseTransportDO> transportsToUpdate = bills.stream()
                .map(ReceiveBillDTO::getTransport)
                .filter(Objects::nonNull)
                .map(PurchaseTransportConvert.INSTANCE::convert2DO)
                .toList();
        if (!transportsToUpdate.isEmpty()) {
            purchaseTransportMapper.updateBatch(transportsToUpdate);
        }
    }

    /**
     * 更新发票信息
     * 从单据中提取发票信息并批量更新
     *
     * @param bills 包含发票信息的单据列表
     */
    private void updateInvoices(List<ReceiveBillDTO> bills) {
        List<PurchaseInvoiceDO> invoicesToUpdate = bills.stream()
                .map(ReceiveBillDTO::getInvoice)
                .filter(Objects::nonNull)
                .map(PurchaseInvoiceConvert.INSTANCE::convert2DO)
                .toList();
        if (!invoicesToUpdate.isEmpty()) {
            purchaseInvoiceMapper.updateBatch(invoicesToUpdate);
        }
    }

    /**
     * 删除原有明细
     * 根据单据编号和租户ID删除关联的明细信息
     *
     * @param bills 需要删除明细的单据列表
     */
    private void deleteExistingDetails(List<ReceiveBillDTO> bills) {
        bills.forEach(bill -> receiveBillDetailMapper.deleteByBillNoAndTenant(bill.getBillNo(), bill.getTenantId()));
    }

    /**
     * 插入运输信息
     * 从单据中提取运输信息并批量插入
     *
     * @param bills 包含运输信息的单据列表
     */
    private void insertTransports(List<ReceiveBillDTO> bills) {
        List<PurchaseTransportDO> transportsToInsert = bills.stream()
                .map(ReceiveBillDTO::getTransport)
                .filter(Objects::nonNull)
                .map(PurchaseTransportConvert.INSTANCE::convert2DO)
                .toList();
        if (!transportsToInsert.isEmpty()) {
            purchaseTransportMapper.insertBatch(transportsToInsert);
        }
    }

    /**
     * 插入发票信息
     * 从单据中提取发票信息并批量插入
     *
     * @param bills 包含发票信息的单据列表
     */
    private void insertInvoices(List<ReceiveBillDTO> bills) {
        List<PurchaseInvoiceDO> invoicesToInsert = bills.stream()
                .map(ReceiveBillDTO::getInvoice)
                .filter(Objects::nonNull)
                .map(PurchaseInvoiceConvert.INSTANCE::convert2DO)
                .toList();

        if (!invoicesToInsert.isEmpty()) {
            purchaseInvoiceMapper.insertBatch(invoicesToInsert);
        }
    }

    /**
     * 处理所有单据的明细
     * 处理流程：
     * 1. 收集所有单据的明细信息
     * 2. 设置明细的单据编号
     * 3. 批量插入明细数据
     *
     * @param receiveBills 需要处理明细的单据列表
     */
    private void handleAllBillDetails(List<ReceiveBillDTO> receiveBills) {
        List<ReceiveBillDetailDO> allDetails = receiveBills.stream()
                .filter(bill -> CollectionUtils.isNotEmpty(bill.getDetails()))
                .flatMap(bill -> bill.getDetails().stream()
                        .peek(detail -> detail.setBillNo(bill.getBillNo()))
                        .map(ReceiveBillDetailConvert.INSTANCE::convert2DO))
                .toList();

        if (!allDetails.isEmpty()) {
            receiveBillDetailMapper.insertBatch(allDetails);
        }
    }

    /**
     * 处理拒收逻辑
     * 处理流程：
     * 1. 前置校验：检查单据是否有明细
     * 2. 查找有效拒收明细：收货数量小于到货数量的明细
     * 3. 处理全单拒收情况：如果所有明细都拒收，则更新单据状态
     * 4. 拒收处理：根据单据类型获取对应的处理器处理拒收
     *
     * @param receiveBill 需要处理拒收的收货单
     */
    private void handleReject(ReceiveBillDTO receiveBill) {
        // 1. 前置校验：检查单据是否有明细
        if (CollectionUtils.isEmpty(receiveBill.getDetails())) {
            log.debug("单据[{}]无明细，跳过拒收处理", receiveBill.getBillNo());
            return;
        }

        // 2. 查找有效拒收明细：收货数量小于到货数量的明细
        List<ReceiveBillDetailDTO> rejectDetails = findValidRejectDetails(receiveBill.getDetails());
        if (CollectionUtils.isEmpty(rejectDetails)) {
            log.debug("单据[{}]无有效拒收明细，跳过拒收处理", receiveBill.getBillNo());
            return;
        }

        // 3. 处理全单拒收情况：如果所有明细都拒收，则更新单据状态
        if (isFullRejection(receiveBill.getDetails(), rejectDetails)) {
            log.info("单据[{}]触发全单拒收", receiveBill.getBillNo());
            receiveBill.setStatus(REJECTED.getCode());
        }

        // 4. 拒收处理：根据单据类型获取对应的处理器处理拒收
        if (List.of(REJECTED.getCode(), STORED.getCode()).contains(receiveBill.getStatus())) {
            getRejectHandler(ReceiveBillTypeEnum.fromCode(receiveBill.getBillType()))
                    .ifPresent(handler -> handler.handle(receiveBill, rejectDetails));
        }
    }

    /**
     * 判断是否全单拒收
     * 判断条件：
     * 1. 拒收明细数量等于总明细数量
     * 2. 所有拒收明细的收货数量都等于0
     *
     * @param allDetails    所有明细列表
     * @param rejectDetails 拒收明细列表
     * @return true: 全单拒收; false: 部分拒收
     */
    private boolean isFullRejection(List<ReceiveBillDetailDTO> allDetails,
            List<ReceiveBillDetailDTO> rejectDetails) {
        return rejectDetails.size() == allDetails.size() &&
                rejectDetails.stream()
                        .allMatch(detail -> detail.getReceiveQuantity().compareTo(BigDecimal.ZERO) == 0);
    }

    /**
     * 查找有效拒收明细
     * 有效拒收条件：
     * 1. 到货数量不为空
     * 2. 收货数量不为空
     * 3. 收货数量小于到货数量
     *
     * @param details 需要检查的明细列表
     * @return 符合条件的拒收明细列表
     */
    private List<ReceiveBillDetailDTO> findValidRejectDetails(List<ReceiveBillDetailDTO> details) {
        return details.stream()
                .filter(detail -> detail.getArriveQuantity() != null &&
                        detail.getReceiveQuantity() != null &&
                        detail.getReceiveQuantity().compareTo(detail.getArriveQuantity()) < 0)
                .toList();
    }

    /**
     * 获取拒收处理器
     * 根据单据类型获取对应的拒收处理器
     *
     * @param billType 单据类型
     * @return 对应的拒收处理器，如果不存在则返回空
     */
    private Optional<RejectHandler> getRejectHandler(ReceiveBillTypeEnum billType) {
        return rejectHandlers.stream()
                .filter(handler -> handler.supportReceiveBillTypes().contains(billType))
                .findFirst();
    }

    /**
     * 更新已存在的单据
     * 处理流程：
     * 1. 更新主表信息
     * 2. 更新运输信息（如果存在）
     * 3. 更新发票信息（如果存在）
     * 4. 删除原有明细
     *
     * @param receiveBill 需要更新的收货单
     */
    private void updateExistingBill(ReceiveBillDTO receiveBill) {
        // 1. 更新主表信息
        receiveBillMapper.updateById(ReceiveBillConvert.INSTANCE.convert2DO(receiveBill));

        // 2. 更新运输信息（如果存在）
        Optional.ofNullable(receiveBill.getTransport())
                .ifPresent(transport -> {
                    PurchaseTransportDO transportDO = PurchaseTransportConvert.INSTANCE.convert2DO(transport);
                    purchaseTransportMapper.updateById(transportDO);
                });

        // 3. 更新发票信息（如果存在）
        Optional.ofNullable(receiveBill.getInvoice())
                .ifPresent(invoice -> {
                    PurchaseInvoiceDO invoiceDO = PurchaseInvoiceConvert.INSTANCE.convert2DO(invoice);
                    purchaseInvoiceMapper.updateById(invoiceDO);
                });

        // 4. 删除原有明细
        receiveBillDetailMapper.deleteByBillNoAndTenant(receiveBill.getBillNo(), receiveBill.getTenantId());
    }

    /**
     * 插入新单据
     * 处理流程：
     * 1. 插入主表信息
     * 2. 插入运输信息（如果存在）
     * 3. 插入发票信息（如果存在）
     *
     * @param receiveBill 需要插入的收货单
     */
    private void insertNewBill(ReceiveBillDTO receiveBill) {
        // 1. 插入主表信息
        receiveBillMapper.insert(ReceiveBillConvert.INSTANCE.convert2DO(receiveBill));

        // 2. 插入运输信息（如果存在）
        Optional.ofNullable(receiveBill.getTransport())
                .ifPresent(transport -> {
                    PurchaseTransportDO transportDO = PurchaseTransportConvert.INSTANCE.convert2DO(transport);
                    purchaseTransportMapper.insert(transportDO);
                });

        // 3. 插入发票信息（如果存在）
        Optional.ofNullable(receiveBill.getInvoice())
                .ifPresent(invoice -> {
                    PurchaseInvoiceDO invoiceDO = PurchaseInvoiceConvert.INSTANCE.convert2DO(invoice);
                    purchaseInvoiceMapper.insert(invoiceDO);
                });
    }

    /**
     * 处理单据明细
     * 为明细设置单据编号并批量插入
     *
     * @param receiveBill 包含明细的收货单
     */
    private void handleBillDetails(ReceiveBillDTO receiveBill) {
        Optional.ofNullable(receiveBill.getDetails())
                .filter(CollectionUtils::isNotEmpty)
                .ifPresent(details -> {
                    // 1. 设置单据编号
                    details.forEach(detail -> detail.setBillNo(receiveBill.getBillNo()));
                    // 2. 批量插入明细
                    receiveBillDetailMapper.insertBatch(ReceiveBillDetailConvert.INSTANCE.convert2DOList(details));
                });
    }

    /**
     * 校验回退单据状态是否合法
     * 检查单据状态是否在允许回退的状态列表中
     *
     * @param receiveBill 需要校验的收货单
     * @throws ServiceException 如果状态不合法则抛出异常
     */
    private void validateRollbackStatus(ReceiveBillDTO receiveBill) {
        Optional.of(receiveBill.getStatus())
                .filter(INVALID_ROLLBACK_STATUSES::contains)
                .ifPresent(status -> {
                    throw exception(RECEIVE_BILL_ROLLBACK_STATUS_ERROR);
                });
    }

    /**
     * 拆分收货单
     * 根据药品类型（中药/西药）将收货单拆分成多个单据
     *
     * @param receiveBill 需要拆分的收货单
     * @return 拆分后的收货单列表
     */
    private List<ReceiveBillDTO> splitReceiveBill(ReceiveBillDTO receiveBill) {
        // 1. 按药品类型分组
        Map<Integer, List<ReceiveBillDetailDTO>> detailsByType = receiveBill.getDetails().stream()
                .collect(Collectors
                        .groupingBy(detail -> TCM_FLAG.contains(detail.getExt().getProductInfo().getFirstCategory())
                                ? MedicineTypeEnum.CHINESE_MEDICINE.getCode()
                                : MedicineTypeEnum.NON_CHINESE_MEDICINE.getCode()));

        // 2. 创建收货单
        return detailsByType.values().stream()
                .map(details -> ReceiveBillConvert.INSTANCE.convert2SplitDO(receiveBill, details))
                .toList();
    }

    /**
     * 更新采购单状态及可退数量
     * 处理流程：
     * 1. 获取关联收货单列表
     * 2. 预计算数量映射
     * 3. 根据单据类型分别处理
     *
     * @param receiveBill 收货单
     */
    private void updatePurchaseBillAfterReceive(ReceiveBillDTO receiveBill) {
        String sourceBillNo = receiveBill.getSourceBillNo();
        if (StringUtils.isBlank(sourceBillNo))
            return;

        // 1. 获取关联收货单列表（添加空集合保护）
        List<ReceiveBillDTO> receiveBills = receiveBillMapper.getReceiveBillWithDetailsBySourceBill(
                sourceBillNo, receiveBill.getTenantId());
        if (CollectionUtils.isEmpty(receiveBills))
            return;

        // 2. 预计算数量映射
        Map<String, BigDecimal> receivedQtyMap = createQuantityMap(receiveBills,
                ReceiveBillDetailDTO::getReceiveAmount);
        Map<String, BigDecimal> warehouseQtyMap = createQuantityMap(receiveBills,
                ReceiveBillDetailDTO::getWarehouseQuantity);
        Map<String, BigDecimal> currentWarehouseQtyMap = createQuantityMap(List.of(receiveBill),
                ReceiveBillDetailDTO::getWarehouseQuantity);

        // 3. 根据类型处理
        switch (ReceiveBillTypeEnum.fromCode(receiveBill.getBillType())) {
            case DISTRIBUTION_RECEIVE -> handleDistribution(receiveBill, receivedQtyMap, warehouseQtyMap);
            case PURCHASE_ORDER_RECEIVE, REQUISITION_RECEIVE -> handleStandardPurchase(receiveBill,
                    receivedQtyMap, warehouseQtyMap, currentWarehouseQtyMap);
            default -> log.warn("未处理的收货单类型: {}", receiveBill.getBillType());
        }
    }

    /**
     * 处理铺货收货逻辑
     * 处理流程：
     * 1. 构建采购明细
     * 2. 计算总发货数量
     * 3. 构建采购单
     * 4. 远程调用确认分销收货
     *
     * @param receiveBill     收货单
     * @param receivedQtyMap  已收货数量映射
     * @param warehouseQtyMap 已入库数量映射
     */
    private void handleDistribution(ReceiveBillDTO receiveBill,
            Map<String, BigDecimal> receivedQtyMap,
            Map<String, BigDecimal> warehouseQtyMap) {
        // 1. 构建采购明细
        List<PurchaseBillDetailDTO> details = buildPurchaseDetails(receivedQtyMap, warehouseQtyMap,
                Collections.emptyMap(), true);

        // 2. 计算总发货数量
        BigDecimal totalDelivered = details.stream()
                .map(PurchaseBillDetailDTO::getDeliveredQuantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 3. 构建采购单
        PurchaseBillDO purchaseBill = PurchaseBillDO.builder()
                .purchaseBillNo(receiveBill.getSourceBillNo())
                .deliveredQuantity(totalDelivered)
                .build();
        purchaseBill.setTenantId(receiveBill.getHeadTenantId());

        // 4. 远程调用确认分销收货
        PurchaseBillSaveReqVO reqVO = PurchaseBillConvert.INSTANCE.convert2VO(purchaseBill);
        CommonResult<Boolean> result = cloudServiceClient
                .call(() -> cloudServiceClient.getClient(PurchaseClient.class).confirmDistributionReceive(reqVO));
        if (!result.isSuccess()) {
            log.error("云端采购单更新失败: {}", result.getMsg());
        }
    }

    /**
     * 处理标准采购逻辑
     * 处理流程：
     * 1. 构建采购明细
     * 2. 计算总可退数量
     * 3. 构建采购单
     * 4. 重新计算采购单状态
     * 5. 更新采购单
     *
     * @param receiveBill            收货单
     * @param receivedQtyMap         已收货数量映射
     * @param warehouseQtyMap        已入库数量映射
     * @param currentWarehouseQtyMap 当前入库数量映射
     */
    private void handleStandardPurchase(ReceiveBillDTO receiveBill,
            Map<String, BigDecimal> receivedQtyMap,
            Map<String, BigDecimal> warehouseQtyMap,
            Map<String, BigDecimal> currentWarehouseQtyMap) {
        // 1. 构建采购明细
        List<PurchaseBillDetailDTO> details = buildPurchaseDetails(receivedQtyMap, warehouseQtyMap,
                currentWarehouseQtyMap, false);

        // 2. 计算总可退数量
        BigDecimal totalReturnable = details.stream()
                .map(PurchaseBillDetailDTO::getReturnableQuantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 3. 构建采购单
        PurchaseBillDTO purchaseBill = PurchaseBillDTO.builder()
                .purchaseBillNo(receiveBill.getSourceBillNo())
                .deliveredQuantity(totalReturnable)
                .returnableQuantity(totalReturnable)
                .details(details)
                .build();

        // 4. 重新计算采购单状态
        purchaseBill = purchaseBillService.recomputePurchaseBillStatusWithQuantities(purchaseBill);

        // 5. 更新采购单
        purchaseBillService.updatePurchaseBill(purchaseBill);
    }

    /**
     * 创建数量映射
     * 将收货单列表中的明细按照商品编码分组，并计算指定数量的总和
     *
     * @param receiveBills      收货单列表
     * @param quantityExtractor 数量提取函数
     * @return 商品编码到数量的映射
     */
    private Map<String, BigDecimal> createQuantityMap(List<ReceiveBillDTO> receiveBills,
            Function<ReceiveBillDetailDTO, BigDecimal> quantityExtractor) {
        return receiveBills.stream()
                .flatMap(bill -> bill.getDetails().stream())
                .collect(Collectors.groupingBy(
                        ReceiveBillDetailDTO::getProductPref,
                        Collectors.mapping(quantityExtractor,
                                Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
    }

    /**
     * 构建采购明细
     * 根据数量映射构建采购明细列表
     *
     * @param receivedQtyMap         已收货数量映射
     * @param warehouseQtyMap        已入库数量映射
     * @param currentWarehouseQtyMap 当前入库数量映射
     * @param isDistribution         是否为分销类型
     * @return 采购明细列表
     */
    private List<PurchaseBillDetailDTO> buildPurchaseDetails(Map<String, BigDecimal> receivedQtyMap,
            Map<String, BigDecimal> warehouseQtyMap,
            Map<String, BigDecimal> currentWarehouseQtyMap,
            boolean isDistribution) {
        List<PurchaseBillDetailDTO> details = new ArrayList<>();
        receivedQtyMap.forEach((key, value) -> {
            BigDecimal warehouseQty = warehouseQtyMap.getOrDefault(key, BigDecimal.ZERO);
            PurchaseBillDetailDTO.PurchaseBillDetailDTOBuilder detail = PurchaseBillDetailDTO.builder()
                    .productPref(key)
                    .deliveredQuantity(value)
                    .warehouseQuantity(warehouseQty);
            if (!isDistribution) {
                detail.returnableQuantity(currentWarehouseQtyMap.getOrDefault(key, BigDecimal.ZERO));
            }
            details.add(detail.build());
        });
        return details;
    }
}