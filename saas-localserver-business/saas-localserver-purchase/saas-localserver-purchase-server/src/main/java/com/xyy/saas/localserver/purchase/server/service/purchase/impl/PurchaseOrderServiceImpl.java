package com.xyy.saas.localserver.purchase.server.service.purchase.impl;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillDTO;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillPageReqDTO;
import com.xyy.saas.localserver.purchase.enums.returned.ReturnBillStatusEnum;
import com.xyy.saas.localserver.purchase.server.convert.purchase.PurchaseBillConvert;
import com.xyy.saas.localserver.purchase.server.convert.purchase.PurchaseBillDetailConvert;
import com.xyy.saas.localserver.purchase.server.service.purchase.PurchaseBillService;
import com.xyy.saas.localserver.purchase.server.service.purchase.PurchaseOrderService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;
import java.util.Objects;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.localserver.purchase.enums.ErrorCodeConstants.REQUISITION_BILL_STATUS_NOT_SUPPORT_REVOKE;

/**
 * 采购订单 Service 实现类
 * <p>
 * 主要功能：
 * 1. 采购订单基础操作（保存、删除、更新、查询）
 * 2. 采购订单状态管理
 * 3. 采购订单分页查询
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Slf4j
public class PurchaseOrderServiceImpl implements PurchaseOrderService {

    // ========== 依赖注入 ==========
    @Resource
    private PurchaseBillService purchaseBillService;

    // ========== 1. 基础操作 ==========

    /**
     * 保存采购订单
     *
     * @param saveDTO 待保存的采购订单信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePurchaseOrder(PurchaseBillDTO saveDTO) {
        // 1. 填充商品信息
        PurchaseBillDetailConvert.INSTANCE.fillProductInfo(saveDTO.getDetails());

        // 2. 拆单
        List<PurchaseBillDTO> purchaseBills = PurchaseBillConvert.INSTANCE.splitBill(saveDTO);

        // 3. 填充采购内容
        purchaseBills.forEach(PurchaseBillConvert.INSTANCE::fillPurchaseContent);

        // 4. 保存单据信息
        purchaseBillService.savePurchaseBills(purchaseBills);

        // 5. TODO: 如果提交，则创建审批流
        if (saveDTO.getSubmitted()) {
            // 如果单体则直接更新采购状态为已审批待发货（待收货）
        }
    }

    /**
     * 撤销采购订单
     *
     * @param revokeDTO 待撤销的采购订单信息
     */
    @Override
    public void revokeRequisition(PurchaseBillDTO revokeDTO) {
        // 1. 校验原单据状态
        PurchaseBillDTO originalRequisitionBill = purchaseBillService.getPurchaseBill(revokeDTO.getId());
        if (!Objects.equals(originalRequisitionBill.getStatus(), ReturnBillStatusEnum.PENDING_APPROVAL.getCode())) {
            throw exception(REQUISITION_BILL_STATUS_NOT_SUPPORT_REVOKE);
        }

        // 2. 更新版本号
        revokeDTO.setVersion(originalRequisitionBill.getVersion());

        // 3. 更新单据
        purchaseBillService.updatePurchaseBill(revokeDTO);
    }

    /**
     * 删除采购订单
     *
     * @param id 采购订单编号
     */
    @Override
    public void deletePurchaseOrder(Long id) {
        purchaseBillService.deletePurchaseBill(id);
    }

    /**
     * 更新采购订单并刷新业务状态
     *
     * @param updateDTO 待更新的采购订单信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePurchaseOrderWithStatusRefresh(PurchaseBillDTO updateDTO) {
        // 1. 执行状态和数量的重新计算
        PurchaseBillDTO updatedBill = purchaseBillService.recomputePurchaseBillStatusWithQuantities(updateDTO);

        // 2. 执行带版本校验的更新
        purchaseBillService.updatePurchaseBill(updatedBill);
    }

    // ========== 2. 查询操作 ==========

    /**
     * 获得采购订单
     *
     * @param id 采购订单编号
     * @return 采购订单信息
     */
    @Override
    public PurchaseBillDTO getPurchaseOrder(Long id) {
        return purchaseBillService.getPurchaseBill(id);
    }

    /**
     * 获得采购订单和详情
     *
     * @param billNo   采购订单号
     * @param tenantId 租户ID
     * @return 采购订单信息（包含详情）
     */
    @Override
    public PurchaseBillDTO getPurchaseOrderWithDetails(String billNo, Long tenantId) {
        return purchaseBillService.getPurchaseBillWithDetails(billNo, tenantId);
    }

    /**
     * 获得采购订单分页
     *
     * @param pageReqDTO 分页查询参数
     * @return 采购订单分页结果
     */
    @Override
    public PageResult<PurchaseBillDTO> getPurchaseOrderPage(PurchaseBillPageReqDTO pageReqDTO) {
        return purchaseBillService.getPurchaseBillPage(pageReqDTO);
    }
}