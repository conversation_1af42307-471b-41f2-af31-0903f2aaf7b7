package com.xyy.saas.localserver.purchase.server.admin.extend.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 管理后台 - 采购-三方erp单据信息分页 Request VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "管理后台 - 采购-三方erp单据信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PurchaseErpBillPageReqVO extends PageParam {

    /** 采购单/收货单号 */
    @Schema(description = "采购单/收货单号")
    private String billNo;

    /** 三方erp订单号 */
    @Schema(description = "三方erp订单号")
    private String erpBillNo;

    /** 三方erp销售单号 */
    @Schema(description = "三方erp销售单号")
    private String salesBillNo;

    /** 三方erp出库单号 */
    @Schema(description = "三方erp出库单号")
    private String outboundBillNo;

    /** 三方erp入库单号 */
    @Schema(description = "三方erp入库单号")
    private String warehouseBillNo;

    /** 三方erp销售退回入库单号(神农XSTHRK) */
    @Schema(description = "三方erp销售退回入库单号(神农XSTHRK)")
    private String refundStorageBillNo;

    /** 综合单据号（单号混合） */
    @Schema(description = "综合单据号（单号混合）")
    private String compositeBillNo;

    /** 三方erp取消原因 */
    @Schema(description = "三方erp取消原因", example = "不香")
    private String cancelReason;

    /** 备注 */
    @Schema(description = "备注", example = "你猜")
    private String remark;

    /** 创建时间 */
    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;
}