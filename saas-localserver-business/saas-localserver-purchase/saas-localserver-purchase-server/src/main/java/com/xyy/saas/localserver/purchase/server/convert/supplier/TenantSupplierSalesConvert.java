package com.xyy.saas.localserver.purchase.server.convert.supplier;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.localserver.purchase.api.supplier.dto.*;
import com.xyy.saas.localserver.purchase.server.admin.supplier.vo.*;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.supplier.TenantSupplierSalesDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 租户供应商销售转换器
 * <p>
 * 主要功能：
 * 1. 基础转换 - VO/DO/DTO之间的转换
 */
@Mapper(uses = { SupplierConvert.class })
public interface TenantSupplierSalesConvert {

    TenantSupplierSalesConvert INSTANCE = Mappers.getMapper(TenantSupplierSalesConvert.class);

    // ========== 1. 基础转换方法 ==========

    /**
     * DTO 转 DO
     * 
     * @param tenantSupplierSalesDTO 租户供应商销售DTO
     * @return 租户供应商销售DO
     */
    TenantSupplierSalesDO convert2DO(TenantSupplierSalesDTO tenantSupplierSalesDTO);

    /**
     * DO 转 DTO
     * 
     * @param tenantSupplierSales 租户供应商销售DO
     * @return 租户供应商销售DTO
     */
    TenantSupplierSalesDTO convert2DTO(TenantSupplierSalesDO tenantSupplierSales);

    /**
     * DO 转 DTO
     * 
     * @param pageResult 租户供应商销售DO分页结果
     * @return 租户供应商销售DTO分页结果
     */
    PageResult<TenantSupplierSalesDTO> convert2DTO(PageResult<TenantSupplierSalesDO> pageResult);

    /**
     * VO 转 DTO
     * 
     * @param saveReqVO 租户供应商销售保存请求VO
     * @return 租户供应商销售DTO
     */
    TenantSupplierSalesDTO convert2DTO(TenantSupplierSalesSaveReqVO saveReqVO);

    /**
     * VO 转 DTO
     * 
     * @param pageReqVO 租户供应商销售分页查询VO
     * @return 租户供应商销售分页查询DTO
     */
    TenantSupplierSalesPageReqDTO convert2DTO(TenantSupplierSalesPageReqVO pageReqVO);

    /**
     * DTO 转 VO
     * 
     * @param tenantSupplierSalesDTO 租户供应商销售DTO
     * @return 租户供应商销售响应VO
     */
    TenantSupplierSalesRespVO convert2VO(TenantSupplierSalesDTO tenantSupplierSalesDTO);

    /**
     * DTO 转 VO
     * 
     * @param pageResultDTO 租户供应商销售DTO分页结果
     * @return 租户供应商销售响应VO分页结果
     */
    PageResult<TenantSupplierSalesRespVO> convert2VO(PageResult<TenantSupplierSalesDTO> pageResultDTO);

    /**
     * DTO列表 转 VO列表
     * 
     * @param list 租户供应商销售DTO列表
     * @return 租户供应商销售响应VO列表
     */
    List<TenantSupplierSalesRespVO> convert2VOList(List<TenantSupplierSalesDTO> list);
}
