package com.xyy.saas.localserver.purchase.server.dal.mysql.supplier;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.xyy.saas.localserver.purchase.api.supplier.dto.TenantSupplierSalesPageReqDTO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.supplier.TenantSupplierSalesDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 租户-供应商-销售人员信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantSupplierSalesMapper extends BaseMapperX<TenantSupplierSalesDO> {

    /**
     * 获得租户-供应商-销售人员信息分页
     * @param reqDTO 分页参数
     * @return 租户-供应商-销售人员信息分页
     */
    default PageResult<TenantSupplierSalesDO> selectPage(TenantSupplierSalesPageReqDTO reqDTO) {
        return selectPage(reqDTO, new LambdaQueryWrapperX<TenantSupplierSalesDO>()
                .eqIfPresent(TenantSupplierSalesDO::getSupplierGuid, reqDTO.getSupplierGuid())
                .likeIfPresent(TenantSupplierSalesDO::getSalesName, reqDTO.getSalesName())
                .eqIfPresent(TenantSupplierSalesDO::getAuthorizedArea, reqDTO.getAuthorizedArea())
                .eqIfPresent(TenantSupplierSalesDO::getAuthorizationNum, reqDTO.getAuthorizationNum())
                .betweenIfPresent(TenantSupplierSalesDO::getAuthorizationNumExpirationDate, reqDTO.getAuthorizationNumExpirationDate())
                .eqIfPresent(TenantSupplierSalesDO::getPhoneNumber, reqDTO.getPhoneNumber())
                .eqIfPresent(TenantSupplierSalesDO::getAuthorizedVarieties, reqDTO.getAuthorizedVarieties())
                .eqIfPresent(TenantSupplierSalesDO::getIdCard, reqDTO.getIdCard())
                .betweenIfPresent(TenantSupplierSalesDO::getIdCardExpirationDate, reqDTO.getIdCardExpirationDate())
                .eqIfPresent(TenantSupplierSalesDO::getIdCardAttachment, reqDTO.getIdCardAttachment())
                .eqIfPresent(TenantSupplierSalesDO::getAuthorizationAttachment, reqDTO.getAuthorizationAttachment())
                .eqIfPresent(TenantSupplierSalesDO::getAuthorizedScope, reqDTO.getAuthorizedScope())
                .betweenIfPresent(TenantSupplierSalesDO::getCreateTime, reqDTO.getCreateTime())
                .orderByDesc(TenantSupplierSalesDO::getId));
    }

}