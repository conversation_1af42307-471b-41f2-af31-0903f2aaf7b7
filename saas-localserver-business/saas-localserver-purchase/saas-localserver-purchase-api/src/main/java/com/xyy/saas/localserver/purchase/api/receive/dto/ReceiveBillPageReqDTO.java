package com.xyy.saas.localserver.purchase.api.receive.dto;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 收货单分页 Request DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "收货单分页 Request DTO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ReceiveBillPageReqDTO extends PageParam {

    /** 收货单号 */
    @Schema(description = "收货（收货/验收/入库）单号")
    private String billNo;

    /** 来源单号 */
    @Schema(description = "来源单号")
    private String sourceBillNo;

    /** 配送出库单号 */
    @Schema(description = "配送出库单号")
    private String deliveryBillNo;

    /** 随货同行单号 */
    @Schema(description = "随货同行单号")
    private String shipmentNo;

    /** 商城订单号 */
    @Schema(description = "商城订单号")
    private String mallOrderNo;

    /** 出库门店租户ID */
    @Schema(description = "出库门店租户id")
    private Long outboundTenantId;

    /** 租户类型 */
    @Schema(description = "租户类型（1-单体门店、2-连锁门店、3-连锁总部）", example = "1")
    private Integer tenantType;

    /** 总部租户ID */
    @Schema(description = "总部租户ID", example = "32278")
    private Long headTenantId;

    /** 综合单据号 */
    @Schema(description = "综合单据号（单号混合）")
    private String compositeBillNo;

    /** 单据类型 */
    @Schema(description = "单据类型（1-采购收货、2-拒收收货、3-退货收货、4-调剂收货、5-要货收货、6-铺货收货）", example = "1")
    private Integer billType;

    /** 采购方式 */
    @Schema(description = "采购方式（1-线下采购、2-无仓（B2B 无仓）、3-共仓（B2C 共仓））")
    private Integer purchaseMode;

    /** 状态 */
    @Schema(description = "状态（1-待收货、2-待验收、3-待入库、4-已入库、5-已拒收）", example = "1")
    private Integer status;

    /** 页面显示状态 */
    @Schema(description = "页面显示状态", example = "1")
    private Integer displayStatus;

    /** 供应商编码 */
    @Schema(description = "供应商编码", example = "13384")
    private String supplierGuid;

    /** 供应商名称 */
    @Schema(description = "供应商名称", example = "赵六")
    private String supplierName;

    /** 供应商销售员 */
    @Schema(description = "供应商销售员")
    private String supplierSales;

    /** 商品种类 */
    @Schema(description = "商品种类")
    private Integer productKind;

    /** 收货数量 */
    @Schema(description = "收货数量")
    private BigDecimal receiveQuantity;

    /** 折扣 */
    @Schema(description = "折扣（百分比）", example = "23792")
    private BigDecimal discount;

    /** 折扣总金额 */
    @Schema(description = "折扣总金额")
    private BigDecimal discountAmount;

    /** 收货内容 */
    @Schema(description = "收货内容")
    private String receiveContent;

    /** 收货金额 */
    @Schema(description = "收货金额（折后总金额）")
    private BigDecimal receiveAmount;

    /** 配送员 */
    @Schema(description = "配送员")
    private String deliverer;

    /** 配送出库时间 */
    @Schema(description = "配送出库时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] deliveryTime;

    /** 收货员 */
    @Schema(description = "收货员")
    private String receiver;

    /** 收货时间 */
    @Schema(description = "收货时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] receiveTime;

    /** 验收员 */
    @Schema(description = "验收员")
    private String accepter;

    /** 验收时间 */
    @Schema(description = "验收时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] acceptTime;

    /** 入库员 */
    @Schema(description = "入库员")
    private String warehouser;

    /** 入库时间 */
    @Schema(description = "入库时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] warehouseTime;

    /** 复核员 */
    @Schema(description = "复核员")
    private String checker;

    /** 复核时间 */
    @Schema(description = "复核时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] checkTime;

    /** 质检员 */
    @Schema(description = "质检员")
    private String qualityInspector;

    /** 质检报告单 */
    @Schema(description = "质检报告单")
    private String qualityInspectionReport;

    /** 备注 */
    @Schema(description = "备注", example = "你说的对")
    private String remark;

    /** 版本 */
    @Schema(description = "版本(乐观锁)")
    private Integer version;

    /** 创建时间 */
    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}