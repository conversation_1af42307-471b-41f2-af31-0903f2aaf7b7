package com.xyy.saas.inquiry.product.api.product.dto.qualification;

import cn.hutool.core.collection.CollUtil;
import com.google.common.base.CaseFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.lang.reflect.Method;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

@Schema(description = "管理后台 - 商品资质信息")
@Data
@Slf4j
public class ProductQualificationInfoDto {

    @Schema(description = "商品编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "19659")
    private String productPref;

    @Schema(description = "质量标准", requiredMode = Schema.RequiredMode.REQUIRED)
    private String qualityStandard;
    @Schema(description = "质量标准 开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate qualityStandardStart;
    @Schema(description = "质量标准 结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate qualityStandardEnd;
    @Schema(description = "质量标准 扩展信息（比如：文件链接）", requiredMode = Schema.RequiredMode.REQUIRED)
    private ProductQualificationExtInfo qualityStandardExt;

    @Schema(description = "执行标准", requiredMode = Schema.RequiredMode.REQUIRED)
    private String executionStandard;
    @Schema(description = "执行标准 开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate executionStandardStart;
    @Schema(description = "执行标准 结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate executionStandardEnd;
    @Schema(description = "执行标准 扩展信息（比如：文件链接）", requiredMode = Schema.RequiredMode.REQUIRED)
    private ProductQualificationExtInfo executionStandardExt;

    @Schema(description = "药品注册证", requiredMode = Schema.RequiredMode.REQUIRED)
    private String drugRegistrationCertificate;
    @Schema(description = "药品注册证 开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate drugRegistrationCertificateStart;
    @Schema(description = "药品注册证 结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate drugRegistrationCertificateEnd;
    @Schema(description = "药品注册证 扩展信息（比如：文件链接）", requiredMode = Schema.RequiredMode.REQUIRED)
    private ProductQualificationExtInfo drugRegistrationCertificateExt;

    @Schema(description = "药品再注册证", requiredMode = Schema.RequiredMode.REQUIRED)
    private String drugReRegistrationCertificate;
    @Schema(description = "药品再注册证 开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate drugReRegistrationCertificateStart;
    @Schema(description = "药品再注册证 结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate drugReRegistrationCertificateEnd;
    @Schema(description = "药品再注册证 扩展信息（比如：文件链接）", requiredMode = Schema.RequiredMode.REQUIRED)
    private ProductQualificationExtInfo drugReRegistrationCertificateExt;

    @Schema(description = "药品补充申请批件", requiredMode = Schema.RequiredMode.REQUIRED)
    private String drugSupplementaryApplicationBatchCertificate;
    @Schema(description = "药品补充申请批件 开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate drugSupplementaryApplicationBatchCertificateStart;
    @Schema(description = "药品补充申请批件 结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate drugSupplementaryApplicationBatchCertificateEnd;
    @Schema(description = "药品补充申请批件 扩展信息（比如：文件链接）", requiredMode = Schema.RequiredMode.REQUIRED)
    private ProductQualificationExtInfo drugSupplementaryApplicationBatchCertificateExt;

    @Schema(description = "医药产品注册证", requiredMode = Schema.RequiredMode.REQUIRED)
    private String medicinalProductRegistrationCertificate;
    @Schema(description = "医药产品注册证 开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate medicinalProductRegistrationCertificateStart;
    @Schema(description = "医药产品注册证 结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate medicinalProductRegistrationCertificateEnd;
    @Schema(description = "医药产品注册证 扩展信息（比如：文件链接）", requiredMode = Schema.RequiredMode.REQUIRED)
    private ProductQualificationExtInfo medicinalProductRegistrationCertificateExt;

    @Schema(description = "进口药品注册证", requiredMode = Schema.RequiredMode.REQUIRED)
    private String importedDrugRegistrationCertificate;
    @Schema(description = "进口药品注册证 开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate importedDrugRegistrationCertificateStart;
    @Schema(description = "进口药品注册证 结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate importedDrugRegistrationCertificateEnd;
    @Schema(description = "进口药品注册证 扩展信息（比如：文件链接）", requiredMode = Schema.RequiredMode.REQUIRED)
    private ProductQualificationExtInfo importedDrugRegistrationCertificateExt;

    @Schema(description = "进口药品补充申请批件", requiredMode = Schema.RequiredMode.REQUIRED)
    private String importedDrugSupplementaryApplicationBatchCertificate;
    @Schema(description = "进口药品补充申请批件 开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate importedDrugSupplementaryApplicationBatchCertificateStart;
    @Schema(description = "进口药品补充申请批件 结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate importedDrugSupplementaryApplicationBatchCertificateEnd;
    @Schema(description = "进口药品补充申请批件 扩展信息（比如：文件链接）", requiredMode = Schema.RequiredMode.REQUIRED)
    private ProductQualificationExtInfo importedDrugSupplementaryApplicationBatchCertificateExt;

    @Schema(description = "GMP证书", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gmpCertificate;
    @Schema(description = "GMP证书 开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate gmpCertificateStart;
    @Schema(description = "GMP证书 结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate gmpCertificateEnd;
    @Schema(description = "GMP证书 扩展信息（比如：文件链接）", requiredMode = Schema.RequiredMode.REQUIRED)
    private ProductQualificationExtInfo gmpCertificateExt;

    @Schema(description = "进口药品再注册", requiredMode = Schema.RequiredMode.REQUIRED)
    private String importedDrugReRegistrationCertificate;
    @Schema(description = "进口药品再注册 开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate importedDrugReRegistrationCertificateStart;
    @Schema(description = "进口药品再注册 结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate importedDrugReRegistrationCertificateEnd;
    @Schema(description = "进口药品再注册 扩展信息（比如：文件链接）", requiredMode = Schema.RequiredMode.REQUIRED)
    private ProductQualificationExtInfo importedDrugReRegistrationCertificateExt;

    @Schema(description = "药品注册批件", requiredMode = Schema.RequiredMode.REQUIRED)
    private String drugRegistrationBatchCertificate;
    @Schema(description = "药品注册批件 开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate drugRegistrationBatchCertificateStart;
    @Schema(description = "药品注册批件 结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate drugRegistrationBatchCertificateEnd;
    @Schema(description = "药品注册批件 扩展信息（比如：文件链接）", requiredMode = Schema.RequiredMode.REQUIRED)
    private ProductQualificationExtInfo drugRegistrationBatchCertificateExt;

    @Schema(description = "保健食品许可证", requiredMode = Schema.RequiredMode.REQUIRED)
    private String healthProductsLicense;
    @Schema(description = "保健食品许可证 开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate healthProductsLicenseStart;
    @Schema(description = "保健食品许可证 结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate healthProductsLicenseEnd;
    @Schema(description = "保健食品许可证 扩展信息（比如：文件链接）", requiredMode = Schema.RequiredMode.REQUIRED)
    private ProductQualificationExtInfo healthProductsLicenseExt;

    @Schema(description = "消毒产品生产企业卫生许可证", requiredMode = Schema.RequiredMode.REQUIRED)
    private String disinfectantProductSellerHealthCertificate;
    @Schema(description = "消毒产品生产企业卫生许可证 开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate disinfectantProductSellerHealthCertificateStart;
    @Schema(description = "消毒产品生产企业卫生许可证 结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate disinfectantProductSellerHealthCertificateEnd;
    @Schema(description = "消毒产品生产企业卫生许可证 扩展信息（比如：文件链接）", requiredMode = Schema.RequiredMode.REQUIRED)
    private ProductQualificationExtInfo disinfectantProductSellerHealthCertificateExt;

    @Schema(description = "国产特殊用途化妆品卫生许可证批件", requiredMode = Schema.RequiredMode.REQUIRED)
    private String chineseSpecialPurposeMakeupProductHealthCertificateBatchCertificate;
    @Schema(description = "国产特殊用途化妆品卫生许可证批件 开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate chineseSpecialPurposeMakeupProductHealthCertificateBatchCertificateStart;
    @Schema(description = "国产特殊用途化妆品卫生许可证批件 结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate chineseSpecialPurposeMakeupProductHealthCertificateBatchCertificateEnd;
    @Schema(description = "国产特殊用途化妆品卫生许可证批件 扩展信息（比如：文件链接）", requiredMode = Schema.RequiredMode.REQUIRED)
    private ProductQualificationExtInfo chineseSpecialPurposeMakeupProductHealthCertificateBatchCertificateExt;

    @Schema(description = "国产非特殊化妆品备案登记凭证", requiredMode = Schema.RequiredMode.REQUIRED)
    private String chineseNonSpecialPurposeMakeupProductRecordRegistrationCertificate;
    @Schema(description = "国产非特殊化妆品备案登记凭证 开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate chineseNonSpecialPurposeMakeupProductRecordRegistrationCertificateStart;
    @Schema(description = "国产非特殊化妆品备案登记凭证 结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate chineseNonSpecialPurposeMakeupProductRecordRegistrationCertificateEnd;
    @Schema(description = "国产非特殊化妆品备案登记凭证 扩展信息（比如：文件链接）", requiredMode = Schema.RequiredMode.REQUIRED)
    private ProductQualificationExtInfo chineseNonSpecialPurposeMakeupProductRecordRegistrationCertificateExt;

    @Schema(description = "进口特殊用途化妆品卫生许可证批件", requiredMode = Schema.RequiredMode.REQUIRED)
    private String importedSpecialPurposeMakeupProductHealthCertificateBatchCertificate;
    @Schema(description = "进口特殊用途化妆品卫生许可证批件 开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate importedSpecialPurposeMakeupProductHealthCertificateBatchCertificateStart;
    @Schema(description = "进口特殊用途化妆品卫生许可证批件 结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate importedSpecialPurposeMakeupProductHealthCertificateBatchCertificateEnd;
    @Schema(description = "进口特殊用途化妆品卫生许可证批件 扩展信息（比如：文件链接）", requiredMode = Schema.RequiredMode.REQUIRED)
    private ProductQualificationExtInfo importedSpecialPurposeMakeupProductHealthCertificateBatchCertificateExt;

    @Schema(description = "进口非特殊用途化妆品备案登记凭证", requiredMode = Schema.RequiredMode.REQUIRED)
    private String importedNonSpecialPurposeMakeupProductRecordRegistrationCertificate;
    @Schema(description = "进口非特殊用途化妆品备案登记凭证 开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate importedNonSpecialPurposeMakeupProductRecordRegistrationCertificateStart;
    @Schema(description = "进口非特殊用途化妆品备案登记凭证 结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate importedNonSpecialPurposeMakeupProductRecordRegistrationCertificateEnd;
    @Schema(description = "进口非特殊用途化妆品备案登记凭证 扩展信息（比如：文件链接）", requiredMode = Schema.RequiredMode.REQUIRED)
    private ProductQualificationExtInfo importedNonSpecialPurposeMakeupProductRecordRegistrationCertificateExt;

    @Schema(description = "样盒-说明书", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sampleBoxInstructions;
    @Schema(description = "样盒-说明书 开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate sampleBoxInstructionsStart;
    @Schema(description = "样盒-说明书 结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate sampleBoxInstructionsEnd;
    @Schema(description = "样盒-说明书 扩展信息（比如：文件链接）", requiredMode = Schema.RequiredMode.REQUIRED)
    private ProductQualificationExtInfo sampleBoxInstructionsExt;

    /**
     * 构建商品资质信息
     * @param infoList
     * @return
     */
    public static ProductQualificationInfoDto of(List<ProductQualificationInfo> infoList) {
        if (CollUtil.isEmpty(infoList)) {
            return null;
        }
        ProductQualificationInfoDto dto = new ProductQualificationInfoDto();
        // 遍历组装数据
        infoList.forEach(info -> assembleInfoToDto(info, dto));

        return dto;
    }


    public List<ProductQualificationInfo> toInfoList() {
        // 遍历组装数据
        return Arrays.stream(TypeEnum.values()).map(i -> assembleDtoToInfo(this, i)).filter(Objects::nonNull).toList();
    }



    /**
     * 反射组装字段
     * @param dto
     */
    public static void assembleInfoToDto(ProductQualificationInfo info, ProductQualificationInfoDto dto) {
        TypeEnum byCode = TypeEnum.getByCode(info.qualificationType);
        if (byCode == null) {
            return;
        }
        // 字段下划线转驼峰
        String setMethod = CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, "set_" + byCode.name().toLowerCase());
        // 通过反射设置值，获取byCode的枚举名称，然后转成小写（方法名：set + 首字母大写），然后通过反射设置值
        try {
            Method method = ProductQualificationInfoDto.class.getMethod(setMethod, String.class);
            method.invoke(dto, info.qualificationInfo);

            Method method1 = ProductQualificationInfoDto.class.getMethod(setMethod + "Start", LocalDate.class);
            method1.invoke(dto, info.startDate);

            Method method2 = ProductQualificationInfoDto.class.getMethod(setMethod + "End", LocalDate.class);
            method2.invoke(dto, info.endDate);

            Method method3 = ProductQualificationInfoDto.class.getMethod(setMethod + "Ext", ProductQualificationExtInfo.class);
            method3.invoke(dto, info.ext);
        } catch (Exception e) {
            log.error("set value error, field: {}, reason: {}", byCode.name(), e.getMessage());
        }
    }

    /**
     * 反射组装字段
     * @param dto
     */
    public static ProductQualificationInfo assembleDtoToInfo(ProductQualificationInfoDto dto, TypeEnum typeEnum) {
        if (typeEnum == null || StringUtils.isBlank(dto.getProductPref())) {
            return null;
        }
        ProductQualificationInfo info = new ProductQualificationInfo()
            .setProductPref(dto.getProductPref())
            .setQualificationType(typeEnum.code);

        // 字段下划线转驼峰
        String getMethod = CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, "get_" + typeEnum.name().toLowerCase());
        // 通过反射设置值，获取byCode的枚举名称，然后转成小写（方法名：set + 首字母大写），然后通过反射设置值
        try {
            Method method = ProductQualificationInfoDto.class.getMethod(getMethod);
            info.setQualificationInfo((String) method.invoke(dto));

            Method method1 = ProductQualificationInfoDto.class.getMethod(getMethod + "Start");
            info.setStartDate((LocalDate) method1.invoke(dto));

            Method method2 = ProductQualificationInfoDto.class.getMethod(getMethod + "End");
            info.setEndDate((LocalDate) method2.invoke(dto));

            Method method3 = ProductQualificationInfoDto.class.getMethod(getMethod + "Ext");
            info.setExt((ProductQualificationExtInfo) method3.invoke(dto));
        } catch (Exception e) {
            log.error("get value error, field: {}, reason: {}", typeEnum.name(), e.getMessage());
        }
        if (info.getQualificationInfo() == null && info.getStartDate() == null && info.getEndDate() == null
            && (info.getExt() == null || CollectionUtils.isEmpty(info.getExt().getUrls()))) {
            return null;
        }
        return info;
    }



    @Schema(description = "管理后台 - 商品资质信息")
    @Data
    public static class ProductQualificationInfo {
        @Schema(description = "商品编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
        private String productPref;
        @Schema(description = "资质类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
        private Integer qualificationType;
        @Schema(description = "资质信息", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
        private String qualificationInfo;
        @Schema(description = "开始日期", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
        private LocalDate startDate;
        @Schema(description = "结束日期", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
        private LocalDate endDate;
        @Schema(description = "扩展信息", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
        private ProductQualificationExtInfo ext;
        @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
        private String remark;
    }

    /**
     * desc 资质类型：质量标准、执行标准、药品注册证、药品再注册证、药品补充申请批件、医药产品注册证、进口药品注册证、进口药品补充申请批件、GMP证书、进口药品再注册、药品注册批件、保健食品许可证、消毒产品生产企业卫生许可证、国产特殊用途化妆品卫生许可证批件、国产非特殊化妆品备案登记凭证、进口特殊用途化妆品卫生许可证批件、进口非特殊用途化妆品备案登记凭证、样盒-说明书
     *
     * <AUTHOR>
     * @version 1.0
     * @since 1.0
     */
    public enum TypeEnum {
        // 质量标准、执行标准、药品注册证、药品再注册证、药品补充申请批件、医药产品注册证、进口药品注册证、进口药品补充申请批件、GMP证书、进口药品再注册、药品注册批件、保健食品许可证、消毒产品生产企业卫生许可证、国产特殊用途化妆品卫生许可证批件、国产非特殊化妆品备案登记凭证、进口特殊用途化妆品卫生许可证批件、进口非特殊用途化妆品备案登记凭证、样盒-说明书
        QUALITY_STANDARD(1, "质量标准"),
        EXECUTION_STANDARD(2, "执行标准"),
        DRUG_REGISTRATION_CERTIFICATE(3, "药品注册证"),
        DRUG_RE_REGISTRATION_CERTIFICATE(4, "药品再注册证"),
        DRUG_SUPPLEMENTARY_APPLICATION_BATCH_CERTIFICATE(5, "药品补充申请批件"),
        MEDICINAL_PRODUCT_REGISTRATION_CERTIFICATE(6, "医药产品注册证"),
        IMPORTED_DRUG_REGISTRATION_CERTIFICATE(7, "进口药品注册证"),
        IMPORTED_DRUG_SUPPLEMENTARY_APPLICATION_BATCH_CERTIFICATE(8, "进口药品补充申请批件"),
        GMP_CERTIFICATE(9, "GMP证书"),
        IMPORTED_DRUG_RE_REGISTRATION_CERTIFICATE(10, "进口药品再注册"),
        DRUG_REGISTRATION_BATCH_CERTIFICATE(11, "药品注册批件"),
        HEALTH_PRODUCTS_LICENSE(12, "保健食品许可证"),
        DISINFECTANT_PRODUCT_SELLER_HEALTH_CERTIFICATE(13, "消毒产品生产企业卫生许可证"),
        CHINESE_SPECIAL_PURPOSE_MAKEUP_PRODUCT_HEALTH_CERTIFICATE_BATCH_CERTIFICATE(14, "国产特殊用途化妆品卫生许可证批件"),
        CHINESE_NON_SPECIAL_PURPOSE_MAKEUP_PRODUCT_RECORD_REGISTRATION_CERTIFICATE(15, "国产非特殊化妆品备案登记凭证"),
        IMPORTED_SPECIAL_PURPOSE_MAKEUP_PRODUCT_HEALTH_CERTIFICATE_BATCH_CERTIFICATE(16, "进口特殊用途化妆品卫生许可证批件"),
        IMPORTED_NON_SPECIAL_PURPOSE_MAKEUP_PRODUCT_RECORD_REGISTRATION_CERTIFICATE(17, "进口非特殊用途化妆品备案登记凭证"),
        SAMPLE_BOX_INSTRUCTIONS(18, "样盒-说明书");

        public final Integer code;
        public final String desc;

        TypeEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static TypeEnum getByCode(Integer code) {
            for (TypeEnum typeEnum : values()) {
                if (typeEnum.code.equals(code)) {
                    return typeEnum;
                }
            }
            return null;
        }

    }
}