package com.xyy.saas.inquiry.product.api.catalog;

import com.xyy.saas.inquiry.product.api.catalog.dto.MedicalCatalogDetailDTO;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public interface MedicalCatalogDetailApi {

    /**
     * 根据项目编码获取监管目录详情
     *
     * @param tenantId
     * @param projectCodes
     * @return
     */
    List<MedicalCatalogDetailDTO> getCatalogDetailByProjectCodes(Long tenantId, List<String> projectCodes);

    default Map<String, MedicalCatalogDetailDTO> getCatalogDetailMapByProjectCodes(Long tenantId, List<String> projectCodes) {
        return getCatalogDetailByProjectCodes(tenantId, projectCodes).stream()
            .collect(Collectors.toMap(MedicalCatalogDetailDTO::getProjectCode, Function.identity(), (a, b) -> b));
    }
}
