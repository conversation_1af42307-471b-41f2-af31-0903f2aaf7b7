package com.xyy.saas.inquiry.product.api.catalog;

import com.xyy.saas.inquiry.product.api.catalog.dto.RegulatoryCatalogDetailDTO;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public interface RegulatoryCatalogDetailApi {

    /**
     * 根据项目编码获取监管目录详情
     *
     * @param tenantId
     * @param projectCodes
     * @return
     */
    List<RegulatoryCatalogDetailDTO> getCatalogDetailByProjectCodes(Long tenantId, List<Long> projectCodes);

    default Map<Long, RegulatoryCatalogDetailDTO> getCatalogDetailMapByProjectCodes(Long tenantId, List<Long> projectCodes) {
        return getCatalogDetailByProjectCodes(tenantId, projectCodes).stream()
            .collect(Collectors.toMap(RegulatoryCatalogDetailDTO::getProjectCode, Function.identity(), (a, b) -> b));
    }
}
