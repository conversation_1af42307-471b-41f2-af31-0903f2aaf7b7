package com.xyy.saas.inquiry.product.api.transfer.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
@Accessors(chain = true)
@Schema(description = "商品流转记录 扩展信息 DTO")
public class ProductTransferRecordExtDto implements Serializable, Cloneable {

    @Schema(description = "包装单位", example = "xxx")
    private String unit;

    /**
     * 商品封面图
     */
    @Schema(description = "商品封面图", requiredMode = Schema.RequiredMode.REQUIRED, example = "[]")
    private List<String> coverImages;
    /**
     * 商品外包装图
     */
    @Schema(description = "商品外包装图", requiredMode = Schema.RequiredMode.REQUIRED, example = "[]")
    private List<String> outerPackageImages;
    /**
     * 商品说明书图
     */
    @Schema(description = "商品说明书图", requiredMode = Schema.RequiredMode.REQUIRED, example = "[]")
    private List<String> instructionImages;


    @Override
    public ProductTransferRecordExtDto clone() {
        try {
            ProductTransferRecordExtDto clone = (ProductTransferRecordExtDto) super.clone();
            // 复制此处的可变状态，这样此克隆就不能更改初始克隆的内部项
            clone.coverImages = this.coverImages == null ? null : this.coverImages.stream().toList();
            clone.outerPackageImages = this.outerPackageImages == null ? null : this.outerPackageImages.stream().toList();
            clone.instructionImages = this.instructionImages == null ? null : this.instructionImages.stream().toList();
            return clone;
        } catch (CloneNotSupportedException e) {
            throw new AssertionError("Clone Not Supported: {}", e);
        }
    }
}
